"""
Core configuration management.
All configuration values are loaded from environment variables.
"""

import os
from typing import List, Optional
from functools import lru_cache


class Settings:
    """Application settings loaded from environment variables."""

    def __init__(self):
        # Application Settings
        self.app_name = os.getenv("APP_NAME", "Reality 2.0")
        self.app_version = os.getenv("APP_VERSION", "1.0.0")
        self.app_description = os.getenv("APP_DESCRIPTION", "High-End Web Application")
        self.app_environment = os.getenv("APP_ENVIRONMENT", "development")

        # Server Configuration
        self.backend_host = os.getenv("BACKEND_HOST", "0.0.0.0")
        self.backend_port = int(os.getenv("BACKEND_PORT", "8000"))
        self.backend_reload = os.getenv("BACKEND_RELOAD", "true").lower() == "true"
        self.backend_log_level = os.getenv("BACKEND_LOG_LEVEL", "info")

        # Database Configuration
        self.database_url = os.getenv("DATABASE_URL", "sqlite:///./reality.db")
        self.database_echo = os.getenv("DATABASE_ECHO", "false").lower() == "true"
        self.database_pool_size = int(os.getenv("DATABASE_POOL_SIZE", "10"))
        self.database_max_overflow = int(os.getenv("DATABASE_MAX_OVERFLOW", "20"))

        # Security Settings
        self.secret_key = os.getenv("SECRET_KEY", "your-super-secret-key-change-this-in-production-12345")
        self.algorithm = os.getenv("ALGORITHM", "HS256")
        self.access_token_expire_minutes = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
        self.refresh_token_expire_days = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))

        # CORS Configuration
        cors_origins_str = os.getenv("CORS_ORIGINS", "http://localhost:3000,http://127.0.0.1:3000")
        self.cors_origins = [origin.strip() for origin in cors_origins_str.split(",")]
        self.cors_allow_credentials = os.getenv("CORS_ALLOW_CREDENTIALS", "true").lower() == "true"
        self.cors_allow_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        self.cors_allow_headers = ["*"]

        # Logging Configuration
        self.log_level = os.getenv("LOG_LEVEL", "info")
        self.log_format = os.getenv("LOG_FORMAT", "text")
        self.log_file_path = os.getenv("LOG_FILE_PATH", "./logs/app.log")
        self.log_max_size = int(os.getenv("LOG_MAX_SIZE", "10485760"))
        self.log_backup_count = int(os.getenv("LOG_BACKUP_COUNT", "5"))

        # Create necessary directories
        os.makedirs("./logs", exist_ok=True)
        os.makedirs("./uploads", exist_ok=True)

    @property
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self.app_environment.lower() == "development"

    @property
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return self.app_environment.lower() == "production"

    @property
    def is_testing(self) -> bool:
        """Check if running in testing mode."""
        return self.app_environment.lower() == "testing"

    @property
    def database_url_sync(self) -> str:
        """Get synchronous database URL for Alembic."""
        if self.database_url.startswith("postgresql+asyncpg://"):
            return self.database_url.replace("postgresql+asyncpg://", "postgresql://")
        return self.database_url


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()


# Global settings instance
settings = get_settings()
