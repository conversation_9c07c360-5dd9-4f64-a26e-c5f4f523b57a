<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex min-h-screen items-center justify-center p-4">
      <div class="fixed inset-0 bg-black opacity-50" @click="$emit('close')"></div>
      <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            <slot name="title">Modal Title</slot>
          </h3>
          <div class="text-gray-600 dark:text-gray-300">
            <slot>Modal content</slot>
          </div>
          <div class="mt-6 flex justify-end space-x-3">
            <slot name="actions">
              <BaseButton variant="secondary" @click="$emit('close')">
                Cancel
              </BaseButton>
              <BaseButton variant="primary" @click="$emit('confirm')">
                Confirm
              </BaseButton>
            </slot>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import BaseButton from './BaseButton.vue'

defineEmits<{
  close: []
  confirm: []
}>()
</script>
