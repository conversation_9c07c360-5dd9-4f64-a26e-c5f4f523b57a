# AI Development Rules & Guidelines

This folder contains comprehensive development rules, guidelines, and best practices for building high-end applications with AI assistance.

## 📁 File Organization

### Core Files
- **`AUGMENT_MEMORIES.md`** - Complete development principles, technology-specific rules, and project structure guidelines
- **`FILE_STRUCTURE_RULES.md`** - Detailed file structure rules with examples and templates
- **`CODING_STANDARDS.md`** - Code quality, naming conventions, and best practices
- **`TECHNOLOGY_SPECIFIC.md`** - FastAPI, Vue.js, and framework-specific guidelines

## 🎯 Quick Reference

### Essential Principles
1. **Consistency is King** - Stick to chosen conventions rigidly
2. **Feature-Based Grouping** - Organize by domain/feature over type
3. **Clear Separation of Concerns** - Backend/frontend, business logic/presentation
4. **Comprehensive Testing** - Unit, integration, and E2E tests
5. **Security First** - Input validation, error handling, rate limiting

### Project Structure Template
```
/your-high-end-app/
├── backend/                  # FastAPI backend
│   ├── app/
│   │   ├── main.py          # Entry point
│   │   ├── core/            # Config, DB, security
│   │   ├── api/v1/endpoints/ # Route definitions
│   │   ├── schemas/         # Pydantic models
│   │   ├── crud/            # Database operations
│   │   ├── models/          # SQLAlchemy models
│   │   ├── services/        # Business logic
│   │   ├── tests/           # Test suite
│   │   └── utils/           # Helper functions
│   └── alembic/             # Database migrations
├── frontend/                # Vue.js frontend
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── views/           # Page components
│   │   ├── store/           # State management
│   │   ├── router/          # Navigation
│   │   ├── services/        # API communication
│   │   ├── utils/           # Helper functions
│   │   └── assets/          # Static files
│   └── public/              # Public assets
├── ai-rules/                # This folder - AI development guidelines
├── .env.example             # Environment template
├── docker-compose.yml       # Local development
├── README.md                # Project overview
└── ... other config files
```

### Technology Stack
- **Backend**: Python + FastAPI + SQLAlchemy + Alembic
- **Frontend**: Vue.js 3 + Pinia + Vue Router + Vite
- **Database**: PostgreSQL (recommended) or SQLite (development)
- **Authentication**: JWT tokens with FastAPI security
- **Deployment**: Docker containers + docker-compose

## 🚀 Getting Started

1. **Read `AUGMENT_MEMORIES.md`** for complete development principles
2. **Follow `FILE_STRUCTURE_RULES.md`** for project organization
3. **Apply technology-specific guidelines** from respective sections
4. **Use this folder as reference** during development

## 📋 Checklist for New Projects

### Setup Phase
- [ ] Create monorepo structure with backend/ and frontend/
- [ ] Set up FastAPI with proper directory structure
- [ ] Initialize Vue.js project with recommended organization
- [ ] Configure environment variables and secrets management
- [ ] Set up Docker and docker-compose for local development

### Development Phase
- [ ] Follow naming conventions consistently
- [ ] Implement proper error handling and logging
- [ ] Write tests for all new features
- [ ] Use feature-based file organization
- [ ] Document all significant components and APIs

### Quality Assurance
- [ ] Run linters and formatters (Black, ESLint, Prettier)
- [ ] Ensure all tests pass
- [ ] Validate security measures (input validation, rate limiting)
- [ ] Check performance and optimize where needed
- [ ] Review code for adherence to established patterns

## 🔧 Tools & Configuration

### Python/FastAPI
- **Linting**: Black, Flake8, Ruff
- **Testing**: pytest, pytest-asyncio
- **Type Checking**: mypy
- **Dependencies**: Poetry or pip-tools

### Vue.js/Frontend
- **Build Tool**: Vite (recommended) or Vue CLI
- **Linting**: ESLint + Prettier
- **Testing**: Vitest or Jest
- **State Management**: Pinia (Vue 3) or Vuex (Vue 2)

### Development Environment
- **MCP Server**: `npx @modelcontextprotocol/server-filesystem`
- **Containerization**: Docker + docker-compose
- **Version Control**: Git with conventional commits
- **IDE**: VS Code with recommended extensions

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Vue.js Guide](https://vuejs.org/guide/)
- [Pinia Documentation](https://pinia.vuejs.org/)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
- [Docker Best Practices](https://docs.docker.com/develop/best-practices/)

## 🤝 Contributing

When adding new rules or guidelines:
1. Follow the established file structure
2. Update this README with new content
3. Ensure consistency with existing principles
4. Add examples where helpful
5. Test guidelines on real projects

---

**Remember**: These rules are living documents. Update them as you learn and discover better practices, but always maintain consistency within each project.
