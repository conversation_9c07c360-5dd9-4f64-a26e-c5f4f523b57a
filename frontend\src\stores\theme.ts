/**
 * Theme store for managing dark/light mode and theme preferences.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // State
  const isDarkMode = ref(false)
  const systemPreference = ref<'light' | 'dark'>('light')
  const userPreference = ref<'light' | 'dark' | 'system'>('system')
  
  // Computed
  const currentTheme = computed(() => {
    if (userPreference.value === 'system') {
      return systemPreference.value
    }
    return userPreference.value
  })
  
  const effectiveTheme = computed(() => {
    return isDarkMode.value ? 'dark' : 'light'
  })
  
  // Actions
  const setTheme = (theme: 'light' | 'dark' | 'system') => {
    userPreference.value = theme
    applyTheme()
    savePreference()
  }
  
  const toggleTheme = () => {
    if (userPreference.value === 'system') {
      setTheme(systemPreference.value === 'dark' ? 'light' : 'dark')
    } else {
      setTheme(userPreference.value === 'dark' ? 'light' : 'dark')
    }
  }
  
  const applyTheme = () => {
    const shouldBeDark = userPreference.value === 'system' 
      ? systemPreference.value === 'dark'
      : userPreference.value === 'dark'
    
    isDarkMode.value = shouldBeDark
    
    if (shouldBeDark) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }
  
  const detectSystemPreference = () => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    systemPreference.value = mediaQuery.matches ? 'dark' : 'light'
    
    // Listen for changes
    mediaQuery.addEventListener('change', (e) => {
      systemPreference.value = e.matches ? 'dark' : 'light'
      if (userPreference.value === 'system') {
        applyTheme()
      }
    })
  }
  
  const savePreference = () => {
    try {
      localStorage.setItem('theme-preference', userPreference.value)
    } catch (error) {
      console.warn('Failed to save theme preference:', error)
    }
  }
  
  const loadPreference = () => {
    try {
      const saved = localStorage.getItem('theme-preference')
      if (saved && ['light', 'dark', 'system'].includes(saved)) {
        userPreference.value = saved as 'light' | 'dark' | 'system'
      }
    } catch (error) {
      console.warn('Failed to load theme preference:', error)
    }
  }
  
  const initializeTheme = () => {
    detectSystemPreference()
    loadPreference()
    applyTheme()
  }
  
  return {
    // State
    isDarkMode,
    systemPreference,
    userPreference,
    
    // Computed
    currentTheme,
    effectiveTheme,
    
    // Actions
    setTheme,
    toggleTheme,
    initializeTheme,
  }
})
