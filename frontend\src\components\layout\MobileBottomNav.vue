<template>
  <nav class="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-2">
    <div class="flex justify-around">
      <router-link
        v-for="item in navItems"
        :key="item.name"
        :to="item.href"
        class="flex flex-col items-center space-y-1 p-2 text-xs"
        :class="isActive(item.href) ? 'text-primary-600' : 'text-gray-500'"
      >
        <component :is="item.icon" class="h-5 w-5" />
        <span>{{ item.name }}</span>
      </router-link>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { HomeIcon, ChartBarIcon, DocumentTextIcon, UserIcon } from '@heroicons/vue/24/outline'

const route = useRoute()

const navItems = [
  { name: 'Home', href: '/', icon: HomeIcon },
  { name: 'Analytics', href: '/analytics', icon: ChartBarIcon },
  { name: 'Docs', href: '/documents', icon: DocumentTextIcon },
  { name: 'Profile', href: '/profile', icon: UserIcon },
]

const isActive = (href: string) => {
  return route.path === href
}
</script>
