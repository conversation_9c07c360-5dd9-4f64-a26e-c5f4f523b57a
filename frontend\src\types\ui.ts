/**
 * UI-related TypeScript type definitions.
 */

// Breakpoint types
export type Breakpoint = 'sm' | 'md' | 'lg' | 'xl' | '2xl'

// UI preferences interface
export interface UIPreferences {
  sidebarCollapsed: boolean
  showMobileBottomNav: boolean
  reducedMotion: boolean
  highContrast: boolean
  fontSize: 'small' | 'medium' | 'large' | 'extra-large'
  compactMode: boolean
}

// Component size variants
export type ComponentSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl'

// Component variants
export type ComponentVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'

// Button specific types
export interface ButtonProps {
  variant?: ComponentVariant
  size?: ComponentSize
  loading?: boolean
  disabled?: boolean
  fullWidth?: boolean
  icon?: boolean
}

// Modal types
export interface ModalProps {
  id: string
  title?: string
  size?: ComponentSize | 'full'
  closable?: boolean
  persistent?: boolean
  showHeader?: boolean
  showFooter?: boolean
}

// Navigation types
export interface NavigationItem {
  id: string
  name: string
  href: string
  icon?: any
  current?: boolean
  badge?: string | number
  children?: NavigationItem[]
  permissions?: string[]
  external?: boolean
}

// Layout types
export interface LayoutConfig {
  sidebar: {
    width: string
    collapsedWidth: string
    breakpoint: Breakpoint
  }
  header: {
    height: string
    fixed: boolean
  }
  footer: {
    height: string
    fixed: boolean
  }
}

// Responsive utilities
export interface ResponsiveValue<T> {
  base?: T
  sm?: T
  md?: T
  lg?: T
  xl?: T
  '2xl'?: T
}

// Animation types
export type AnimationType = 'fade' | 'slide' | 'scale' | 'bounce' | 'pulse'
export type AnimationDirection = 'up' | 'down' | 'left' | 'right' | 'in' | 'out'

// Theme types
export interface ThemeColors {
  primary: string
  secondary: string
  accent: string
  success: string
  warning: string
  error: string
  info: string
  gray: string
}

// Form types
export interface FormFieldProps {
  name: string
  label?: string
  placeholder?: string
  type?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  error?: string
  helpText?: string
  size?: ComponentSize
}

// Table types
export interface TableColumn {
  key: string
  label: string
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
  render?: (value: any, row: any) => string
}

export interface TableProps {
  columns: TableColumn[]
  data: any[]
  loading?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  selectable?: boolean
  selectedRows?: any[]
  pagination?: boolean
  pageSize?: number
  currentPage?: number
  totalItems?: number
}

// Card types
export interface CardProps {
  title?: string
  subtitle?: string
  image?: string
  actions?: any[]
  loading?: boolean
  hoverable?: boolean
  clickable?: boolean
}

// Notification types
export interface NotificationProps {
  id: string
  title?: string
  message: string
  type: ComponentVariant
  duration?: number
  persistent?: boolean
  actions?: NotificationAction[]
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
}

export interface NotificationAction {
  label: string
  action: () => void
  style?: ComponentVariant
}

// Loading states
export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

// Skeleton types
export interface SkeletonProps {
  width?: string | number
  height?: string | number
  circle?: boolean
  lines?: number
  animated?: boolean
}

// Dropdown types
export interface DropdownItem {
  id: string
  label: string
  value: any
  icon?: any
  disabled?: boolean
  divider?: boolean
  href?: string
  action?: () => void
}

export interface DropdownProps {
  items: DropdownItem[]
  trigger?: 'click' | 'hover'
  placement?: 'bottom' | 'top' | 'left' | 'right'
  offset?: number
  disabled?: boolean
}

// Tooltip types
export interface TooltipProps {
  content: string
  placement?: 'top' | 'bottom' | 'left' | 'right'
  trigger?: 'hover' | 'click' | 'focus'
  delay?: number
  disabled?: boolean
}

// Progress types
export interface ProgressProps {
  value: number
  max?: number
  size?: ComponentSize
  color?: ComponentVariant
  striped?: boolean
  animated?: boolean
  showLabel?: boolean
}

// Avatar types
export interface AvatarProps {
  src?: string
  alt?: string
  name?: string
  size?: ComponentSize
  shape?: 'circle' | 'square'
  fallback?: string
}

// Badge types
export interface BadgeProps {
  content: string | number
  variant?: ComponentVariant
  size?: ComponentSize
  dot?: boolean
  max?: number
}

// Pagination types
export interface PaginationProps {
  currentPage: number
  totalPages: number
  pageSize: number
  totalItems: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
  disabled?: boolean
}
