<template>
  <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-3">
    <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
      <div class="flex items-center space-x-4">
        <span>&copy; 2025 {{ appConfig.name }}. All rights reserved.</span>
      </div>
      <div class="flex items-center space-x-4">
        <span>v{{ appVersion }}</span>
        <span>{{ appConfig.app_environment }}</span>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@stores/app'

const appStore = useAppStore()
const appConfig = computed(() => appStore.config)
const appVersion = computed(() => appStore.appVersion)
</script>
