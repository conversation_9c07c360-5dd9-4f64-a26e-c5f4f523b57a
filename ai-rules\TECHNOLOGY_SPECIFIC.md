# Technology-Specific Guidelines

## Fast<PERSON>I Backend Development

### Asynchronous Programming
- **Use async/await extensively**: FastAPI is built on ASGI and thrives on asynchronous operations
- Use `async def` for any function that performs I/O-bound tasks (database calls, external API requests, file operations)
- Be mindful of blocking operations - if a function is synchronous (heavy CPU-bound computation), FastAPI will run it in a thread pool
- Avoid too many synchronous blocking calls as they can degrade performance

### Request & Response Models
- **Define Pydantic BaseModel classes** for all incoming request bodies, query parameters, and path parameters
- **Create response models** for all outgoing responses to ensure consistent API contracts
- **Leverage Python's type hints extensively** with Pydantic for clear, maintainable, and self-documenting API contracts
- Use Pydantic's validation features for automatic data validation, serialization, and deserialization

### Dependency Injection & Architecture
- **Utilize FastAPI's powerful dependency injection system** to manage connections (database sessions), authentication, and other shared resources
- Makes code more modular and testable
- **Break down API into logical modules** using APIRouter instances to prevent main.py from becoming monolithic
- Improves maintainability and organization

### Database Integration
- **Choose appropriate ORM**: SQLAlchemy with Alembic for migrations is recommended
- **Integrate database cleanly** with proper session management
- **Manage database schema changes** with migrations (Alembic for SQLAlchemy) to ensure smooth deployments
- **Use connection pooling** for efficient database connections
- Implement proper transaction handling

### Background Tasks & Middleware
- **For long-running or non-critical tasks** (sending emails, processing images), use FastAPI's BackgroundTasks or dedicated task queue system (Celery with Redis/RabbitMQ)
- Avoid blocking the main request-response cycle
- **Use Starlette middleware** for cross-cutting concerns like logging, security headers, CORS, and request/response processing

### Configuration Management
- **Load configuration from environment variables** using Pydantic's Settings management
- Keep secrets out of codebase and allow for easy deployment to different environments
- Use structured configuration classes for type safety and validation

### API Design Best Practices
- Use proper HTTP status codes
- Implement consistent error response formats
- Version your APIs (e.g., /api/v1/)
- Use OpenAPI documentation features extensively
- Implement proper authentication and authorization
- Add request/response logging for debugging

## Vue.js Frontend Development

### Component Architecture
- **Design UI as a tree of reusable, self-contained components**
- Each component should have a single responsibility
- **Use props for parent-to-child communication** and events for child-to-parent communication
- Keep components as pure and predictable as possible

### State Management
- **For complex applications**, use dedicated state management library:
  - **Pinia** (recommended for Vue 3)
  - **Vuex** (for Vue 2)
- **Keep components "dumb" (presentational)** and let state management handle the "smart" (data and logic) parts
- Centralize and manage application state properly
- Use modules to organize state by feature/domain

### Routing & Navigation
- **Use Vue Router** for declarative client-side routing
- **Organize routes logically** and use lazy loading for routes to improve initial load performance
- Implement proper route guards for authentication and authorization
- Use nested routes for complex page structures

### API Communication
- **Use consistent method for making API calls** (Axios or native fetch API)
- **Centralize API calls** in dedicated service or module to keep components clean and promote reusability
- **Handle API errors gracefully** in the frontend with proper user feedback
- Implement loading states and error handling for data fetching
- Use interceptors for common functionality (auth tokens, error handling)

### Data Management & Reactivity
- **Decide when and where to fetch data** (component mount, route change, user action)
- **Implement loading states and error handling** for data fetching
- **Understand Vue's reactivity system** and use computed properties for derived state
- **Use watchers judiciously** for side effects when state changes
- Avoid direct mutation of props

### Styling & UI
- **Choose consistent styling approach**:
  - CSS Modules for component-scoped styles
  - Scoped CSS in Single File Components
  - Utility-first CSS like Tailwind CSS
  - Component libraries (Vuetify, Quasar, etc.)
- Maintain design system consistency
- Use CSS custom properties for theming

### Performance Optimization
- **Lazy load components and routes** to reduce initial bundle size
- **Use Webpack/Vite's code splitting** to reduce bundle size
- **Optimize images** for web (compression, proper formats, responsive images)
- **For large lists**, consider virtual scrolling to render only visible items
- Use `v-memo` and `v-once` directives appropriately
- Implement proper caching strategies

### Production Optimization
- **Minify and compress** JavaScript, CSS, and HTML for production
- Use tree shaking to eliminate dead code
- Implement proper error boundaries and error reporting
- Use service workers for offline functionality where appropriate
- Monitor bundle size and performance metrics

### Accessibility & UX
- **Design and build UI with accessibility in mind**:
  - Proper semantic HTML
  - ARIA attributes where needed
  - Keyboard navigation support
  - Screen reader compatibility
- Implement proper focus management
- Use appropriate color contrast ratios
- Provide alternative text for images

## Database & ORM Guidelines

### SQLAlchemy Best Practices
- Use declarative base for model definitions
- Implement proper relationships between models
- Use migrations for all schema changes
- Implement proper indexing strategies
- Use connection pooling and session management
- Handle database transactions properly

### Query Optimization
- Avoid N+1 query problems using eager loading
- Use database indexes effectively
- Implement pagination for large datasets
- Use raw SQL for complex queries when ORM becomes inefficient
- Monitor query performance and optimize slow queries

## Docker & Deployment

### Containerization
- Use multi-stage builds for production images
- Minimize image size by using appropriate base images
- Implement proper health checks
- Use .dockerignore to exclude unnecessary files
- Set appropriate user permissions and security contexts

### Environment Management
- Use docker-compose for local development
- Implement proper environment variable management
- Use secrets management for sensitive data
- Implement proper logging and monitoring
- Use container orchestration (Kubernetes, Docker Swarm) for production

## Security Best Practices

### Authentication & Authorization
- Implement proper JWT token handling
- Use secure password hashing (bcrypt, Argon2)
- Implement proper session management
- Use HTTPS everywhere in production
- Implement proper CORS policies

### Input Validation
- Validate all input on both frontend and backend
- Sanitize user input to prevent XSS attacks
- Use parameterized queries to prevent SQL injection
- Implement rate limiting and request throttling
- Use CSRF protection where appropriate

### Data Protection
- Encrypt sensitive data at rest and in transit
- Implement proper backup and recovery procedures
- Use environment variables for secrets
- Regularly audit dependencies for vulnerabilities
- Implement proper logging without exposing sensitive information
