version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: reality-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-reality}
      POSTGRES_USER: ${POSTGRES_USER:-reality_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-reality_password}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - reality-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-reality_user} -d ${POSTGRES_DB:-reality}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: reality-redis
    command: redis-server --appendonly yes ${REDIS_PASSWORD:+--requirepass $REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - reality-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Backend
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: reality-backend
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-reality_user}:${POSTGRES_PASSWORD:-reality_password}@postgres:5432/${POSTGRES_DB:-reality}
      - REDIS_URL=redis://redis:6379/${REDIS_DB:-0}
      - BACKEND_HOST=${BACKEND_HOST:-0.0.0.0}
      - BACKEND_PORT=${BACKEND_PORT:-8000}
      - SECRET_KEY=${SECRET_KEY}
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:3000}
      - APP_ENVIRONMENT=${APP_ENVIRONMENT:-development}
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - reality-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    develop:
      watch:
        - action: sync
          path: ./backend
          target: /app
          ignore:
            - __pycache__/
            - "*.pyc"
            - .pytest_cache/

  # Vue.js Frontend
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      target: development
    container_name: reality-frontend
    environment:
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://localhost:8000}
      - VITE_APP_NAME=${VITE_APP_NAME:-Reality 2.0}
      - VITE_APP_ENVIRONMENT=${APP_ENVIRONMENT:-development}
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - reality-network
    restart: unless-stopped
    develop:
      watch:
        - action: sync
          path: ./frontend/src
          target: /app/src
        - action: rebuild
          path: ./frontend/package.json

  # pgAdmin for database management (development only)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: reality-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_DEFAULT_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_DEFAULT_PASSWORD:-admin123}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "${PGADMIN_PORT:-8080}:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - reality-network
    restart: unless-stopped
    profiles:
      - development

  # Nginx reverse proxy (production)
  nginx:
    image: nginx:alpine
    container_name: reality-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./frontend/dist:/usr/share/nginx/html:ro
    depends_on:
      - backend
      - frontend
    networks:
      - reality-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  reality-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
