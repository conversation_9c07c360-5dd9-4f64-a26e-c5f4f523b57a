/**
 * Main CSS file for Reality 2.0
 * Includes Tailwind CSS, custom properties, and responsive utilities
 */

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* CSS Custom Properties for Dynamic Theming */
:root {
  /* Primary color palette (Blue) */
  --color-primary-50: 239 246 255;
  --color-primary-100: 219 234 254;
  --color-primary-200: 191 219 254;
  --color-primary-300: 147 197 253;
  --color-primary-400: 96 165 250;
  --color-primary-500: 59 130 246;
  --color-primary-600: 37 99 235;
  --color-primary-700: 29 78 216;
  --color-primary-800: 30 64 175;
  --color-primary-900: 30 58 138;
  --color-primary-950: 23 37 84;
  
  /* Secondary color palette (Green) */
  --color-secondary-50: 236 253 245;
  --color-secondary-100: 209 250 229;
  --color-secondary-200: 167 243 208;
  --color-secondary-300: 110 231 183;
  --color-secondary-400: 52 211 153;
  --color-secondary-500: 16 185 129;
  --color-secondary-600: 5 150 105;
  --color-secondary-700: 4 120 87;
  --color-secondary-800: 6 95 70;
  --color-secondary-900: 6 78 59;
  --color-secondary-950: 2 44 34;
  
  /* Accent color palette (Amber) */
  --color-accent-50: 255 251 235;
  --color-accent-100: 254 243 199;
  --color-accent-200: 253 230 138;
  --color-accent-300: 252 211 77;
  --color-accent-400: 251 191 36;
  --color-accent-500: 245 158 11;
  --color-accent-600: 217 119 6;
  --color-accent-700: 180 83 9;
  --color-accent-800: 146 64 14;
  --color-accent-900: 120 53 15;
  --color-accent-950: 69 26 3;
  
  /* Layout variables */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 64px;
  --header-height: 64px;
  --footer-height: 48px;
  --container-max-width: 1200px;
  
  /* Animation variables */
  --transition-duration: 0.2s;
  --transition-timing: ease-in-out;
  
  /* Border radius */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Dark mode color adjustments */
.dark {
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Base styles */
html {
  scroll-behavior: smooth;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid rgb(59 130 246);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: rgb(209 213 219) rgb(243 244 246);
}

.dark * {
  scrollbar-color: rgb(75 85 99) rgb(31 41 55);
}

/* Responsive font sizes */
@media (max-width: 640px) {
  html {
    font-size: 14px;
  }
}

@media (min-width: 1024px) {
  html {
    font-size: 16px;
  }
}

@media (min-width: 1536px) {
  html {
    font-size: 18px;
  }
}

/* High contrast mode */
.high-contrast {
  --color-primary-500: 0 0 0;
  --color-primary-600: 0 0 0;
  filter: contrast(150%);
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Compact mode */
.compact-mode {
  --header-height: 48px;
  --footer-height: 32px;
}

.compact-mode .card {
  @apply p-4;
}

.compact-mode .btn {
  @apply px-3 py-1.5 text-xs;
}

/* Font size preferences */
[data-font-size="small"] {
  font-size: 0.875rem;
}

[data-font-size="medium"] {
  font-size: 1rem;
}

[data-font-size="large"] {
  font-size: 1.125rem;
}

[data-font-size="extra-large"] {
  font-size: 1.25rem;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  * {
    color: black !important;
    background: white !important;
  }
  
  .card {
    border: 1px solid #ccc !important;
    box-shadow: none !important;
  }
}

/* Loading states */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Skeleton loading */
.skeleton {
  @apply bg-gray-200 dark:bg-gray-700 animate-pulse rounded;
}

/* Utility classes */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

.container-responsive {
  @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.grid-responsive {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
}

.flex-responsive {
  @apply flex flex-col sm:flex-row gap-4 sm:gap-6;
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Interactive states */
.interactive {
  @apply transition-all duration-200 ease-in-out;
}

.interactive:hover {
  @apply transform scale-105;
}

.interactive:active {
  @apply transform scale-95;
}

/* Glass morphism effect */
.glass {
  @apply bg-white/10 backdrop-blur-md border border-white/20;
}

.dark .glass {
  @apply bg-gray-900/10 border-gray-700/20;
}
