<template>
  <router-link
    :to="item.href"
    :class="navItemClasses"
    @click="handleClick"
  >
    <component
      :is="item.icon"
      :class="iconClasses"
    />
    <span v-if="!collapsed" class="ml-3 truncate">
      {{ item.name }}
    </span>
    <span
      v-if="item.badge && !collapsed"
      class="ml-auto inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full"
    >
      {{ item.badge }}
    </span>
  </router-link>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

interface NavItem {
  name: string
  href: string
  icon: any
  current: boolean
  badge?: string | number
}

interface Props {
  item: NavItem
  collapsed: boolean
}

const props = defineProps<Props>()
const route = useRoute()

const isActive = computed(() => {
  return route.path === props.item.href
})

const navItemClasses = computed(() => [
  'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200',
  isActive.value
    ? 'bg-primary-100 text-primary-900 dark:bg-primary-900 dark:text-primary-100'
    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white',
  props.collapsed ? 'justify-center' : ''
])

const iconClasses = computed(() => [
  'flex-shrink-0 h-5 w-5',
  isActive.value
    ? 'text-primary-500 dark:text-primary-400'
    : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
])

const handleClick = () => {
  // Handle any click logic here
}
</script>
