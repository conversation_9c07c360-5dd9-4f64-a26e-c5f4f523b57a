# Augment Agent Memories - Development Rules & Guidelines

## Core Development Principles

### Always save everything to an MCP server
- Implement comprehensive logging and monitoring from the start
- Separate configuration from code
- Never hardcode sensitive information (API keys, database credentials) or environment-specific settings

### Code Predictability
- Code should behave in a way that is predictable and intuitive to other developers (and future you)
- Every module, class, or function should have one, and only one, reason to change

### Naming & Clarity
- Use meaningful variable, function, and class names
- Avoid abbreviations and magic numbers/strings
- Prioritize clarity over cleverness

### Component Design
- Design components to be independent of each other (loose coupling)
- Ensure that elements within a component are strongly related to each other (high cohesion)
- Leverage existing, well-tested libraries and frameworks where appropriate, but understand what they do

### Flexibility & Engineering
- Design for flexibility and extensibility, but avoid over-engineering
- Consistency (PEP 8 for Python): Adhere to established style guides
- Use linters and formatters (Black, Flake8, Ruff for Python; ESLint, Prettier for JavaScript/Vue.js)

### Code Simplicity
- Write code that is easy to understand and reason about
- Avoid unnecessary complexity
- DRY (Don't Repeat Yourself): Abstract common logic into reusable functions, components, or services
- KISS (Keep It Simple, Stupid): Strive for the simplest possible solution that meets requirements
- YAGNI (You Aren't Gonna Need It): Don't add functionality unless explicitly needed right now

### Modular Architecture
- Break down applications into smaller, independent, and loosely coupled modules, components, or services
- Each module should have a single, well-defined responsibility
- Backend: separate routing, business logic, data access
- Frontend: separate components for UI elements, data fetching, state management

### Testing Strategy
- Write unit, integration, and end-to-end tests for your code
- Design components and functions to be easily testable (inject dependencies, avoid global state)
- Aim for high test coverage

### Error Handling & Security
- Implement comprehensive error handling
- Catch exceptions gracefully and provide informative error messages
- Validate all incoming data (frontend and backend)
- Use logging effectively to monitor application health and debug issues
- Input Validation & Sanitization: Always validate and sanitize user input
- Sensitive Data Handling: Never store sensitive data in plain text
- Rate Limiting: Implement rate limiting to prevent abuse and DDoS attacks

### Performance & Documentation
- Identify and address performance bottlenecks using profiling tools
- Implement caching where appropriate (Redis for backend, client-side caching for Vue.js)
- Use docstrings for Python functions, classes, and modules
- Maintain a clear README.md for the project
- FastAPI's automatic OpenAPI documentation is a huge advantage – leverage it!
- Document Vue.js components and their props/events

## FastAPI Specific Rules

### Asynchronous Operations
- FastAPI is built on ASGI and thrives on asynchronous operations
- Use async def for any function that performs I/O-bound tasks
- Be mindful of blocking operations

### Request & Response Models
- Define Pydantic BaseModel classes for all incoming request bodies, query parameters, and path parameters
- Define models for outgoing responses
- Leverage Python's type hints extensively with Pydantic

### Dependency Injection & Routing
- Utilize FastAPI's powerful dependency injection system
- Break down API into logical modules using APIRouter instances
- Choose appropriate ORM (SQLAlchemy with Alembic for migrations)
- Manage database schema changes with migrations
- Use connection pooling for efficient database connections

### Background Tasks & Middleware
- For long-running or non-critical tasks, use FastAPI's BackgroundTasks or dedicated task queue
- Use Starlette middleware for cross-cutting concerns
- Load configuration from environment variables using Pydantic's Settings management

## Vue.js Specific Rules

### Component Design
- Design UI as a tree of reusable, self-contained components
- Each component should have a single responsibility
- Use props for parent-to-child communication and events for child-to-parent communication

### State Management
- For complex applications, use dedicated state management library (Pinia for Vue 3, Vuex for Vue 2)
- Keep components "dumb" (presentational) and let state management handle "smart" parts

### Routing & API Calls
- Use Vue Router for declarative client-side routing
- Organize routes logically and use lazy loading for routes
- Use consistent method for making API calls (Axios or fetch API)
- Centralize API calls in dedicated service or module
- Handle API errors gracefully in the frontend

### Performance & Styling
- Understand Vue's reactivity system and use computed properties for derived state
- Use watchers judiciously for side effects when state changes
- Choose consistent styling approach (CSS Modules, scoped CSS, Tailwind CSS)
- Lazy load components and routes
- Use code splitting to reduce bundle size
- Optimize images for web
- For large lists, consider virtual scrolling

### Accessibility & Production
- Design and build UI with accessibility in mind
- Use proper semantic HTML, ARIA attributes, keyboard navigation
- Minify and compress JavaScript, CSS, and HTML for production

## Project Structure Rules

### Development Environment
- User runs MCP filesystem server using command 'npx @modelcontextprotocol/server-filesystem'
- Points to augment-projects directory and configures it in Augment settings under tools/MCP

### High-End Application Structure
- For high-end Python/FastAPI + Vue.js applications, use monorepo structure
- Separate backend/ and frontend/ directories
- Organized by clear separation of concerns for maintainability and scalability

### FastAPI Backend Structure
- app/main.py (entry), core/ (config/db/security), api/v1/endpoints/ (routes)
- schemas/ (Pydantic models), crud/ (DB operations), models/ (SQLAlchemy)
- services/ (business logic), tests/, utils/

### Vue.js Frontend Structure
- src/components/ (reusable UI), views/ (page components), store/ (Pinia/Vuex)
- router/, services/ (API calls), utils/, assets/
- Clear separation between presentation and business logic

## File Structure Rules (16 Core Rules)

### Rule 1: Rigid Convention Adherence
- Once you choose a naming convention (singular vs plural directories, PascalCase for components, snake_case for Python modules), stick to it rigidly throughout the entire project
- Inconsistency is a major source of confusion and development slowdown

### Rule 2: Balanced Hierarchy
- Avoid excessively deep nesting (more than 3-4 levels) which makes paths long and navigation cumbersome
- Avoid overly flat structures that cram too many unrelated files into one directory

### Rule 3: Feature-Based Grouping
- For larger applications, prioritize grouping files by feature/domain over grouping purely by type
- When working on a feature, all related files should be co-located for easier development and reasoning

### Rule 4: Descriptive Naming
- Directory and file names should clearly indicate their content or purpose
- Avoid generic names like 'utils' if content can be more specific (e.g., email_utils, file_upload_helpers)

### Rule 5: Dedicated Configuration
- Dedicate a specific location (e.g., config/ directory or core/config.py) for all application configuration
- Make it easy to understand and modify application behavior without digging through code

### Rule 6: Consistent Test Placement
- Either have a top-level tests/ directory that mirrors your source code structure, or place tests directly next to the code they test
- Choose one approach and stick to it consistently

### Rule 7: Third-Party Isolation
- Never directly modify third-party library files
- If you need to patch or extend something, wrap it in your own code or use official extension points to prevent breaking updates

### Rule 8: Generated Files Separation
- All generated files (build outputs, compiled code, cache files, logs) should be directed to specific, easily identifiable directories (dist/, build/, target/, logs/)
- Be explicitly ignored by version control

### Rule 9: Essential Root Files
- Essential project-level files should be in the root directory (README.md, LICENSE, .gitignore, package.json, pyproject.toml, Dockerfile, docker-compose.yml)
- Provide immediate context and instructions

### Rule 10: Clean Root Directory
- Keep the root directory as clean as possible, only placing essential project configuration and documentation files there
- Move application code into dedicated source directories (src/, app/)

### Rule 11: Clear Content Indication
- Folder and file names should clearly indicate their content or purpose while being as short as possible
- Avoid ambiguity and names like _final_final, new_copy, temp

### Rule 12: Build Artifacts Separation
- Keep automatically generated files (build outputs, cache files, logs, dependency installations) separate from source code
- Use directories like dist/, build/, node_modules/, __pycache__/, logs/

### Rule 13: Documentation Requirements
- Every significant directory, and certainly the project root, should have a README.md explaining its purpose
- Include how to use its contents, and any important conventions for onboarding and maintenance

### Rule 14: Relative Path Usage
- Where possible, use relative file paths within your codebase (./components/Button.vue) instead of absolute paths
- Make your project more portable and easier to refactor

### Rule 15: Safe Character Usage
- Use hyphens (-), underscores (_), or camelCase for names
- Avoid spaces or special characters (!, @, #, $, %, &, *, etc.) which can cause issues across different operating systems and build tools

### Rule 16: Date Format Consistency
- For files that are versioned or dated (logs, backups, reports), use a consistent, sortable date format like YYYY-MM-DD or YYYYMMDD
- Ensure chronological sorting and easy identification
