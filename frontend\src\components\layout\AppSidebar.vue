<template>
  <aside
    :class="sidebarClasses"
    class="bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ease-in-out"
  >
    <!-- Sidebar header -->
    <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700">
      <!-- Logo and app name -->
      <router-link
        to="/"
        class="flex items-center space-x-3 text-xl font-bold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
      >
        <img
          :src="appConfig.logoUrl"
          :alt="`${appConfig.name} Logo`"
          class="h-8 w-8 rounded"
        />
        <span v-if="!uiStore.isSidebarCollapsed" class="truncate">
          {{ appConfig.name }}
        </span>
      </router-link>
      
      <!-- Collapse toggle (desktop only) -->
      <button
        v-if="!uiStore.isMobile"
        @click="uiStore.toggleSidebarCollapse"
        class="p-2 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        :title="uiStore.isSidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'"
      >
        <ChevronLeftIcon
          v-if="!uiStore.isSidebarCollapsed"
          class="h-5 w-5"
        />
        <ChevronRightIcon
          v-else
          class="h-5 w-5"
        />
      </button>
    </div>
    
    <!-- Navigation -->
    <nav class="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
      <!-- Main navigation items -->
      <div class="space-y-1">
        <SidebarNavItem
          v-for="item in mainNavItems"
          :key="item.name"
          :item="item"
          :collapsed="uiStore.isSidebarCollapsed"
        />
      </div>
      
      <!-- Divider -->
      <div
        v-if="secondaryNavItems.length > 0"
        class="border-t border-gray-200 dark:border-gray-700 my-4"
      ></div>
      
      <!-- Secondary navigation items -->
      <div v-if="secondaryNavItems.length > 0" class="space-y-1">
        <SidebarNavItem
          v-for="item in secondaryNavItems"
          :key="item.name"
          :item="item"
          :collapsed="uiStore.isSidebarCollapsed"
        />
      </div>
    </nav>
    
    <!-- Sidebar footer -->
    <div class="border-t border-gray-200 dark:border-gray-700 p-4">
      <!-- User profile section -->
      <div v-if="authStore.isAuthenticated" class="flex items-center space-x-3">
        <img
          :src="authStore.user?.avatar || '/default-avatar.png'"
          :alt="authStore.user?.name || 'User'"
          class="h-8 w-8 rounded-full"
        />
        <div v-if="!uiStore.isSidebarCollapsed" class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
            {{ authStore.user?.name || 'User' }}
          </p>
          <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
            {{ authStore.user?.email }}
          </p>
        </div>
      </div>
      
      <!-- Theme toggle -->
      <div class="mt-3 flex items-center justify-between">
        <span
          v-if="!uiStore.isSidebarCollapsed"
          class="text-sm text-gray-700 dark:text-gray-300"
        >
          Dark mode
        </span>
        <button
          @click="themeStore.toggleTheme"
          class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          :class="themeStore.isDarkMode ? 'bg-blue-600' : 'bg-gray-200'"
          :title="themeStore.isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'"
        >
          <span
            class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"
            :class="themeStore.isDarkMode ? 'translate-x-6' : 'translate-x-1'"
          ></span>
        </button>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/24/outline'
import {
  HomeIcon,
  UserIcon,
  CogIcon,
  DocumentTextIcon,
  ChartBarIcon,
  BellIcon,
} from '@heroicons/vue/24/outline'

import { useAppStore } from '@stores/app'
import { useUiStore } from '@stores/ui'
import { useThemeStore } from '@stores/theme'
import { useAuthStore } from '@stores/auth'
import SidebarNavItem from './SidebarNavItem.vue'

// Stores
const appStore = useAppStore()
const uiStore = useUiStore()
const themeStore = useThemeStore()
const authStore = useAuthStore()

// App configuration
const appConfig = computed(() => appStore.config)

// Sidebar classes for responsive behavior
const sidebarClasses = computed(() => {
  const baseClasses = 'fixed inset-y-0 left-0 z-30 flex flex-col'
  
  if (uiStore.isMobile) {
    // Mobile: full overlay or hidden
    return [
      baseClasses,
      'w-64',
      uiStore.isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full',
      'lg:translate-x-0 lg:static lg:inset-0'
    ]
  } else {
    // Desktop: collapsible
    return [
      baseClasses,
      'static inset-0',
      uiStore.isSidebarCollapsed ? 'w-16' : 'w-64'
    ]
  }
})

// Navigation items configuration
const mainNavItems = computed(() => [
  {
    name: 'Dashboard',
    href: '/',
    icon: HomeIcon,
    current: false,
    badge: null,
  },
  {
    name: 'Analytics',
    href: '/analytics',
    icon: ChartBarIcon,
    current: false,
    badge: null,
  },
  {
    name: 'Documents',
    href: '/documents',
    icon: DocumentTextIcon,
    current: false,
    badge: '12',
  },
  {
    name: 'Notifications',
    href: '/notifications',
    icon: BellIcon,
    current: false,
    badge: authStore.unreadNotifications > 0 ? authStore.unreadNotifications.toString() : null,
  },
])

const secondaryNavItems = computed(() => [
  {
    name: 'Profile',
    href: '/profile',
    icon: UserIcon,
    current: false,
    badge: null,
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: CogIcon,
    current: false,
    badge: null,
  },
])
</script>

<style scoped>
/* Custom scrollbar for navigation */
nav::-webkit-scrollbar {
  width: 4px;
}

nav::-webkit-scrollbar-track {
  @apply bg-transparent;
}

nav::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

nav::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}
</style>
