/**
 * Main application store for global state management.
 * Handles app configuration, loading states, and error management.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { AppConfig, AppError } from '@types/app'

export const useAppStore = defineStore('app', () => {
  // State
  const isLoading = ref(false)
  const error = ref<AppError | null>(null)
  const isInitialized = ref(false)
  
  // Configuration loaded from environment variables
  const config = ref<AppConfig>({
    // Application branding
    name: import.meta.env.VITE_APP_NAME || 'Reality 2.0',
    tagline: import.meta.env.VITE_APP_TAGLINE || 'The Future of Web Applications',
    logoUrl: import.meta.env.VITE_APP_LOGO_URL || '/logo.svg',
    faviconUrl: import.meta.env.VITE_APP_FAVICON_URL || '/favicon.ico',
    
    // API configuration
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
    apiVersion: import.meta.env.VITE_API_VERSION || 'v1',
    apiTimeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000,
    
    // Theme configuration
    theme: {
      primaryColor: import.meta.env.VITE_THEME_PRIMARY_COLOR || '#3B82F6',
      secondaryColor: import.meta.env.VITE_THEME_SECONDARY_COLOR || '#10B981',
      accentColor: import.meta.env.VITE_THEME_ACCENT_COLOR || '#F59E0B',
      errorColor: import.meta.env.VITE_THEME_ERROR_COLOR || '#EF4444',
      warningColor: import.meta.env.VITE_THEME_WARNING_COLOR || '#F97316',
      successColor: import.meta.env.VITE_THEME_SUCCESS_COLOR || '#22C55E',
      infoColor: import.meta.env.VITE_THEME_INFO_COLOR || '#06B6D4',
    },
    
    // Layout configuration
    layout: {
      sidebarWidth: import.meta.env.VITE_LAYOUT_SIDEBAR_WIDTH || '280px',
      headerHeight: import.meta.env.VITE_LAYOUT_HEADER_HEIGHT || '64px',
      footerHeight: import.meta.env.VITE_LAYOUT_FOOTER_HEIGHT || '48px',
      containerMaxWidth: import.meta.env.VITE_LAYOUT_CONTAINER_MAX_WIDTH || '1200px',
    },
    
    // Feature flags
    features: {
      darkMode: import.meta.env.VITE_FEATURE_DARK_MODE === 'true',
      notifications: import.meta.env.VITE_FEATURE_NOTIFICATIONS === 'true',
      realTime: import.meta.env.VITE_FEATURE_REAL_TIME === 'true',
      pwa: import.meta.env.VITE_FEATURE_PWA === 'true',
      analytics: import.meta.env.VITE_FEATURE_ANALYTICS === 'true',
    },
    
    // Performance settings
    performance: {
      cacheDuration: parseInt(import.meta.env.VITE_CACHE_DURATION) || 300000,
      debounceDelay: parseInt(import.meta.env.VITE_DEBOUNCE_DELAY) || 300,
      paginationSize: parseInt(import.meta.env.VITE_PAGINATION_SIZE) || 20,
      infiniteScrollThreshold: parseInt(import.meta.env.VITE_INFINITE_SCROLL_THRESHOLD) || 100,
    },
    
    // Social media and external links
    social: {
      github: import.meta.env.VITE_SOCIAL_GITHUB || '',
      twitter: import.meta.env.VITE_SOCIAL_TWITTER || '',
      linkedin: import.meta.env.VITE_SOCIAL_LINKEDIN || '',
    },
    
    // Contact information
    contact: {
      email: import.meta.env.VITE_CONTACT_EMAIL || '',
      supportUrl: import.meta.env.VITE_SUPPORT_URL || '',
      documentationUrl: import.meta.env.VITE_DOCUMENTATION_URL || '',
    },
    
    // Analytics configuration
    analytics: {
      googleId: import.meta.env.VITE_ANALYTICS_GOOGLE_ID || '',
      mixpanelToken: import.meta.env.VITE_ANALYTICS_MIXPANEL_TOKEN || '',
      hotjarId: import.meta.env.VITE_ANALYTICS_HOTJAR_ID || '',
    },
  })
  
  // Computed properties
  const apiUrl = computed(() => `${config.value.apiBaseUrl}/api/${config.value.apiVersion}`)
  
  const isDevelopment = computed(() => import.meta.env.DEV)
  const isProduction = computed(() => import.meta.env.PROD)
  
  const appVersion = computed(() => import.meta.env.VITE_APP_VERSION || '1.0.0')
  const buildTime = computed(() => import.meta.env.VITE_BUILD_TIME || new Date().toISOString())
  
  // Actions
  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }
  
  const setError = (errorMessage: string | AppError | null) => {
    if (typeof errorMessage === 'string') {
      error.value = {
        message: errorMessage,
        code: 'GENERIC_ERROR',
        timestamp: new Date().toISOString(),
      }
    } else {
      error.value = errorMessage
    }
  }
  
  const clearError = () => {
    error.value = null
  }
  
  const updateConfig = (newConfig: Partial<AppConfig>) => {
    config.value = { ...config.value, ...newConfig }
  }
  
  const initialize = async () => {
    if (isInitialized.value) return
    
    try {
      setLoading(true)
      
      // Apply CSS custom properties for theming
      applyThemeVariables()
      
      // Apply layout variables
      applyLayoutVariables()
      
      // Initialize analytics if enabled
      if (config.value.features.analytics && config.value.analytics.googleId) {
        await initializeAnalytics()
      }
      
      // Initialize PWA if enabled
      if (config.value.features.pwa) {
        await initializePWA()
      }
      
      isInitialized.value = true
    } catch (err) {
      console.error('Failed to initialize app:', err)
      setError('Failed to initialize application')
    } finally {
      setLoading(false)
    }
  }
  
  const applyThemeVariables = () => {
    const root = document.documentElement
    const theme = config.value.theme
    
    root.style.setProperty('--color-primary', theme.primaryColor)
    root.style.setProperty('--color-secondary', theme.secondaryColor)
    root.style.setProperty('--color-accent', theme.accentColor)
    root.style.setProperty('--color-error', theme.errorColor)
    root.style.setProperty('--color-warning', theme.warningColor)
    root.style.setProperty('--color-success', theme.successColor)
    root.style.setProperty('--color-info', theme.infoColor)
  }
  
  const applyLayoutVariables = () => {
    const root = document.documentElement
    const layout = config.value.layout
    
    root.style.setProperty('--sidebar-width', layout.sidebarWidth)
    root.style.setProperty('--header-height', layout.headerHeight)
    root.style.setProperty('--footer-height', layout.footerHeight)
    root.style.setProperty('--container-max-width', layout.containerMaxWidth)
  }
  
  const initializeAnalytics = async () => {
    // Initialize Google Analytics if ID is provided
    if (config.value.analytics.googleId) {
      // Dynamic import to avoid loading analytics in development
      if (isProduction.value) {
        const { gtag } = await import('@/utils/analytics')
        gtag('config', config.value.analytics.googleId)
      }
    }
  }
  
  const initializePWA = async () => {
    // Register service worker for PWA functionality
    if ('serviceWorker' in navigator && isProduction.value) {
      try {
        await navigator.serviceWorker.register('/sw.js')
        console.log('Service Worker registered successfully')
      } catch (error) {
        console.error('Service Worker registration failed:', error)
      }
    }
  }
  
  return {
    // State
    isLoading,
    error,
    isInitialized,
    config,
    
    // Computed
    apiUrl,
    isDevelopment,
    isProduction,
    appVersion,
    buildTime,
    
    // Actions
    setLoading,
    setError,
    clearError,
    updateConfig,
    initialize,
  }
})
