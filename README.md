# Reality 2.0 - High-End Web Application

A modern, responsive web application built with FastAPI backend and Vue.js 3 frontend, following enterprise-grade development practices.

## 🚀 Technology Stack

### Backend
- **Python 3.11+** with FastAPI
- **SQLAlchemy** with Alembic migrations
- **Pydantic** for data validation
- **PostgreSQL** (production) / SQLite (development)
- **JWT** authentication
- **Docker** containerization

### Frontend
- **Vue.js 3** with Composition API
- **Vite** for build tooling
- **Pinia** for state management
- **Vue Router** for navigation
- **Tailwind CSS** for styling
- **TypeScript** for type safety

## 📁 Project Structure

```
Reality-2.0/
├── backend/                  # FastAPI backend
│   ├── app/
│   │   ├── main.py          # Application entry point
│   │   ├── core/            # Core configuration
│   │   ├── api/v1/          # API routes
│   │   ├── models/          # Database models
│   │   ├── schemas/         # Pydantic schemas
│   │   ├── services/        # Business logic
│   │   └── utils/           # Utilities
│   ├── alembic/             # Database migrations
│   ├── tests/               # Backend tests
│   └── requirements.txt     # Python dependencies
├── frontend/                # Vue.js frontend
│   ├── src/
│   │   ├── components/      # Reusable components
│   │   ├── views/           # Page components
│   │   ├── stores/          # Pinia stores
│   │   ├── router/          # Vue Router
│   │   ├── services/        # API services
│   │   ├── composables/     # Vue composables
│   │   ├── types/           # TypeScript types
│   │   └── assets/          # Static assets
│   ├── public/              # Public assets
│   └── package.json         # Node dependencies
├── ai-rules/                # Development guidelines
├── .env.example             # Environment template
├── docker-compose.yml       # Development setup
├── Dockerfile.backend       # Backend container
├── Dockerfile.frontend      # Frontend container
└── README.md               # This file
```

## 🛠️ Development Setup

### Prerequisites
- **Node.js 18+** and npm/yarn/pnpm
- **Python 3.11+** and pip
- **Docker** and Docker Compose
- **Git** with conventional commits

### Quick Start

1. **Clone and setup environment:**
   ```bash
   git clone <repository-url>
   cd Reality-2.0
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Start with Docker (Recommended):**
   ```bash
   docker-compose up -d
   ```

3. **Or run manually:**
   ```bash
   # Backend
   cd backend
   pip install -r requirements.txt
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   
   # Frontend (new terminal)
   cd frontend
   npm install
   npm run dev
   ```

### Access Points
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Database Admin**: http://localhost:8080 (if using pgAdmin)

## 🔧 Configuration

All configuration is environment-based with no hardcoded values:

### Backend Configuration
- Database connections
- JWT secrets and expiration
- CORS settings
- API rate limiting
- Logging levels

### Frontend Configuration
- API base URLs
- Application branding
- Theme settings
- Feature flags
- Analytics configuration

## 📱 Features

### Responsive Design
- **Mobile-first** approach
- **Adaptive layouts** for all screen sizes
- **Touch-friendly** interactions
- **Accessibility** compliant (WCAG 2.1)

### Modern Architecture
- **Component-based** UI architecture
- **State management** with Pinia
- **Type-safe** development with TypeScript
- **Real-time** updates with WebSocket support
- **Progressive Web App** capabilities

### Security & Performance
- **JWT-based** authentication
- **Input validation** on all endpoints
- **Rate limiting** and DDoS protection
- **Code splitting** and lazy loading
- **Optimized** build pipeline

## 🧪 Testing

### Backend Testing
```bash
cd backend
pytest tests/ -v --cov=app
```

### Frontend Testing
```bash
cd frontend
npm run test:unit
npm run test:e2e
```

## 🚀 Deployment

### Production Build
```bash
# Build frontend
cd frontend && npm run build

# Build Docker images
docker build -f Dockerfile.backend -t reality-backend .
docker build -f Dockerfile.frontend -t reality-frontend .
```

### Environment Variables
See `.env.example` for all required environment variables.

## 📚 Documentation

- [API Documentation](http://localhost:8000/docs) - Interactive API docs
- [Development Guidelines](./ai-rules/README.md) - Coding standards and rules
- [Architecture Decisions](./docs/architecture.md) - Technical decisions
- [Deployment Guide](./docs/deployment.md) - Production deployment

## 🤝 Contributing

1. Follow the [coding standards](./ai-rules/CODING_STANDARDS.md)
2. Use [conventional commits](https://conventionalcommits.org/)
3. Write tests for new features
4. Update documentation as needed
5. Ensure all CI checks pass

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Built with ❤️ following enterprise-grade development practices**
