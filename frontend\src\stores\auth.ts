/**
 * Authentication store for managing user authentication state.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '@/types/app'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isLoading = ref(false)
  const unreadNotifications = ref(0)
  
  // Computed
  const isAuthenticated = computed(() => !!user.value && !!token.value)
  
  // Actions
  const setUser = (userData: User) => {
    user.value = userData
  }
  
  const setToken = (tokenValue: string) => {
    token.value = tokenValue
    // Save to localStorage
    try {
      localStorage.setItem('auth-token', tokenValue)
    } catch (error) {
      console.warn('Failed to save token:', error)
    }
  }
  
  const clearAuth = () => {
    user.value = null
    token.value = null
    unreadNotifications.value = 0
    
    // Clear from localStorage
    try {
      localStorage.removeItem('auth-token')
    } catch (error) {
      console.warn('Failed to clear token:', error)
    }
  }
  
  const checkAuthStatus = async () => {
    // For demo purposes, create a mock user
    // In a real app, this would check with the backend
    try {
      const savedToken = localStorage.getItem('auth-token')
      if (savedToken) {
        token.value = savedToken
        // Mock user data
        user.value = {
          id: '1',
          name: 'Demo User',
          email: '<EMAIL>',
          avatar: '/default-avatar.png',
          role: 'user',
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          preferences: {
            theme: 'system',
            language: 'en',
            timezone: 'UTC',
            notifications: {
              email: true,
              push: true,
              inApp: true,
              frequency: 'immediate'
            }
          }
        }
        unreadNotifications.value = 3
      }
    } catch (error) {
      console.warn('Failed to check auth status:', error)
    }
  }
  
  const login = async (email: string, password: string) => {
    isLoading.value = true
    try {
      // Mock login - replace with real API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockToken = 'mock-jwt-token'
      const mockUser: User = {
        id: '1',
        name: 'Demo User',
        email,
        avatar: '/default-avatar.png',
        role: 'user',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        preferences: {
          theme: 'system',
          language: 'en',
          timezone: 'UTC',
          notifications: {
            email: true,
            push: true,
            inApp: true,
            frequency: 'immediate'
          }
        }
      }
      
      setToken(mockToken)
      setUser(mockUser)
      unreadNotifications.value = 3
      
      return { success: true }
    } catch (error) {
      return { success: false, error: 'Login failed' }
    } finally {
      isLoading.value = false
    }
  }
  
  const logout = async () => {
    isLoading.value = true
    try {
      // Mock logout - replace with real API call
      await new Promise(resolve => setTimeout(resolve, 500))
      clearAuth()
      return { success: true }
    } catch (error) {
      return { success: false, error: 'Logout failed' }
    } finally {
      isLoading.value = false
    }
  }
  
  return {
    // State
    user,
    token,
    isLoading,
    unreadNotifications,
    
    // Computed
    isAuthenticated,
    
    // Actions
    setUser,
    setToken,
    clearAuth,
    checkAuthStatus,
    login,
    logout,
  }
})
