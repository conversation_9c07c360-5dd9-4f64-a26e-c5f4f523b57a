<template>
  <div class="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5">
    <div class="p-4">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white">Notifications</h3>
      <div class="mt-2 space-y-2">
        <div class="p-2 bg-gray-50 dark:bg-gray-700 rounded">
          <p class="text-sm text-gray-900 dark:text-white">Sample notification</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineEmits<{
  close: []
}>()
</script>
