/**
 * Search store for managing search functionality.
 */

import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useSearchStore = defineStore('search', () => {
  // State
  const query = ref('')
  const results = ref([])
  const isSearching = ref(false)
  
  // Actions
  const performSearch = async (searchQuery: string) => {
    query.value = searchQuery
    isSearching.value = true
    
    try {
      // Mock search - replace with real API call
      await new Promise(resolve => setTimeout(resolve, 500))
      results.value = [
        { id: 1, title: 'Search Result 1', description: 'Description 1' },
        { id: 2, title: 'Search Result 2', description: 'Description 2' },
      ]
    } finally {
      isSearching.value = false
    }
  }
  
  const clearSearch = () => {
    query.value = ''
    results.value = []
  }
  
  return {
    query,
    results,
    isSearching,
    performSearch,
    clearSearch,
  }
})
