/**
 * Application-wide TypeScript type definitions.
 * Defines interfaces for configuration, errors, and core app functionality.
 */

// Application configuration interface
export interface AppConfig {
  // Application branding
  name: string
  tagline: string
  logoUrl: string
  faviconUrl: string
  
  // API configuration
  apiBaseUrl: string
  apiVersion: string
  apiTimeout: number
  
  // Theme configuration
  theme: ThemeConfig
  
  // Layout configuration
  layout: LayoutConfig
  
  // Feature flags
  features: FeatureFlags
  
  // Performance settings
  performance: PerformanceConfig
  
  // Social media links
  social: SocialConfig
  
  // Contact information
  contact: ContactConfig
  
  // Analytics configuration
  analytics: AnalyticsConfig
}

// Theme configuration
export interface ThemeConfig {
  primaryColor: string
  secondaryColor: string
  accentColor: string
  errorColor: string
  warningColor: string
  successColor: string
  infoColor: string
}

// Layout configuration
export interface LayoutConfig {
  sidebarWidth: string
  headerHeight: string
  footerHeight: string
  containerMaxWidth: string
}

// Feature flags
export interface FeatureFlags {
  darkMode: boolean
  notifications: boolean
  realTime: boolean
  pwa: boolean
  analytics: boolean
}

// Performance configuration
export interface PerformanceConfig {
  cacheDuration: number
  debounceDelay: number
  paginationSize: number
  infiniteScrollThreshold: number
}

// Social media configuration
export interface SocialConfig {
  github: string
  twitter: string
  linkedin: string
}

// Contact configuration
export interface ContactConfig {
  email: string
  supportUrl: string
  documentationUrl: string
}

// Analytics configuration
export interface AnalyticsConfig {
  googleId: string
  mixpanelToken: string
  hotjarId: string
}

// Error handling
export interface AppError {
  message: string
  code: string
  timestamp: string
  details?: Record<string, any>
}

// API response wrapper
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
  timestamp: string
}

// Pagination
export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  meta: PaginationMeta
}

// Loading states
export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

// Generic entity interface
export interface BaseEntity {
  id: string | number
  createdAt: string
  updatedAt: string
}

// User interface
export interface User extends BaseEntity {
  name: string
  email: string
  avatar?: string
  role: UserRole
  isActive: boolean
  lastLoginAt?: string
  preferences: UserPreferences
}

export type UserRole = 'admin' | 'user' | 'moderator' | 'guest'

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: string
  timezone: string
  notifications: NotificationPreferences
}

export interface NotificationPreferences {
  email: boolean
  push: boolean
  inApp: boolean
  frequency: 'immediate' | 'daily' | 'weekly' | 'never'
}

// Notification interface
export interface Notification extends BaseEntity {
  title: string
  message: string
  type: NotificationType
  isRead: boolean
  userId: string | number
  actionUrl?: string
  metadata?: Record<string, any>
}

export type NotificationType = 'info' | 'success' | 'warning' | 'error' | 'system'

// Navigation interface
export interface NavigationItem {
  name: string
  href: string
  icon?: any
  current: boolean
  badge?: string | number
  children?: NavigationItem[]
  permissions?: string[]
}

// Breadcrumb interface
export interface BreadcrumbItem {
  name: string
  href?: string
  current?: boolean
}

// Modal interface
export interface ModalConfig {
  id: string
  title: string
  size: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closable: boolean
  persistent: boolean
}

// Toast notification interface
export interface Toast {
  id: string
  title?: string
  message: string
  type: NotificationType
  duration: number
  persistent: boolean
  actions?: ToastAction[]
}

export interface ToastAction {
  label: string
  action: () => void
  style?: 'primary' | 'secondary' | 'danger'
}

// Search interface
export interface SearchResult {
  id: string
  title: string
  description: string
  type: string
  url: string
  relevance: number
  metadata?: Record<string, any>
}

export interface SearchFilters {
  type?: string[]
  dateRange?: {
    start: string
    end: string
  }
  tags?: string[]
  author?: string
}

// File upload interface
export interface FileUpload {
  id: string
  name: string
  size: number
  type: string
  url?: string
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
}

// Form validation
export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => boolean | string
}

export interface FormField {
  name: string
  label: string
  type: string
  value: any
  rules: ValidationRule[]
  error?: string
  disabled?: boolean
  placeholder?: string
}

// Chart data interface
export interface ChartDataPoint {
  label: string
  value: number
  color?: string
  metadata?: Record<string, any>
}

export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'doughnut' | 'area'
  data: ChartDataPoint[]
  options: Record<string, any>
}

// Environment types
export type Environment = 'development' | 'staging' | 'production' | 'test'

// Generic utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}
