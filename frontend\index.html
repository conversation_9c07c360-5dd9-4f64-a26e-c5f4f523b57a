<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Reality 2.0 - The Future of Web Applications" />
    <meta name="theme-color" content="#3B82F6" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Inter font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- PWA manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- Apple touch icon -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    
    <title>Reality 2.0</title>
    
    <!-- Prevent FOUC (Flash of Unstyled Content) -->
    <style>
      html {
        font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      /* Loading screen */
      #initial-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f9fafb;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .dark #initial-loader {
        background: #111827;
      }
      
      .loader-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loader when app is ready */
      .app-ready #initial-loader {
        display: none;
      }
    </style>
  </head>
  <body>
    <!-- Initial loading screen -->
    <div id="initial-loader">
      <div class="loader-spinner"></div>
    </div>
    
    <!-- Vue app mount point -->
    <div id="app"></div>
    
    <!-- Vite development script -->
    <script type="module" src="/src/main.ts"></script>
    
    <!-- Remove initial loader when app loads -->
    <script>
      window.addEventListener('load', () => {
        setTimeout(() => {
          document.body.classList.add('app-ready');
        }, 100);
      });
    </script>
  </body>
</html>
