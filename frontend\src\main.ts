/**
 * Main entry point for the Vue.js application.
 * Initializes the app with all necessary plugins, stores, and configurations.
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'

// Styles
import './assets/styles/main.css'

// Global components (optional - register commonly used components)
import LoadingSpinner from '@components/common/LoadingSpinner.vue'
import ErrorBoundary from '@components/common/ErrorBoundary.vue'
import BaseButton from '@components/common/BaseButton.vue'
import BaseInput from '@components/common/BaseInput.vue'
import BaseModal from '@components/common/BaseModal.vue'

// Plugins and utilities
import { setupInterceptors } from '@services/api'
import { registerSW } from '@utils/pwa'

// Create Vue application
const app = createApp(App)

// Create Pinia store
const pinia = createPinia()

// Use plugins
app.use(pinia)
app.use(router)

// Register global components
app.component('LoadingSpinner', LoadingSpinner)
app.component('ErrorBoundary', ErrorBoundary)
app.component('BaseButton', BaseButton)
app.component('BaseInput', BaseInput)
app.component('BaseModal', BaseModal)

// Global properties
app.config.globalProperties.$appName = import.meta.env.VITE_APP_NAME || 'Reality 2.0'
app.config.globalProperties.$appVersion = import.meta.env.VITE_APP_VERSION || '1.0.0'
app.config.globalProperties.$isDev = import.meta.env.DEV
app.config.globalProperties.$isProd = import.meta.env.PROD

// Error handling
app.config.errorHandler = (err, instance, info) => {
  console.error('Global error:', err)
  console.error('Component instance:', instance)
  console.error('Error info:', info)
  
  // Send error to monitoring service in production
  if (import.meta.env.PROD) {
    // Example: Sentry.captureException(err)
  }
}

// Warning handler (development only)
if (import.meta.env.DEV) {
  app.config.warnHandler = (msg, instance, trace) => {
    console.warn('Vue warning:', msg)
    console.warn('Component trace:', trace)
  }
}

// Performance monitoring (development only)
if (import.meta.env.DEV) {
  app.config.performance = true
}

// Initialize application
const initializeApp = async () => {
  try {
    // Setup API interceptors
    setupInterceptors()
    
    // Register service worker for PWA (production only)
    if (import.meta.env.PROD && import.meta.env.VITE_FEATURE_PWA === 'true') {
      await registerSW()
    }
    
    // Mount the application
    app.mount('#app')
    
    console.log(`🚀 ${import.meta.env.VITE_APP_NAME} v${import.meta.env.VITE_APP_VERSION} started`)
    
  } catch (error) {
    console.error('Failed to initialize application:', error)
    
    // Show error message to user
    document.body.innerHTML = `
      <div style="
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        font-family: system-ui, -apple-system, sans-serif;
        background: #f3f4f6;
        color: #374151;
        text-align: center;
        padding: 2rem;
      ">
        <div>
          <h1 style="font-size: 2rem; margin-bottom: 1rem; color: #dc2626;">
            Application Failed to Load
          </h1>
          <p style="margin-bottom: 1rem;">
            We're sorry, but the application failed to initialize properly.
          </p>
          <p style="font-size: 0.875rem; color: #6b7280;">
            Please refresh the page or contact support if the problem persists.
          </p>
          <button 
            onclick="window.location.reload()" 
            style="
              margin-top: 1rem;
              padding: 0.5rem 1rem;
              background: #3b82f6;
              color: white;
              border: none;
              border-radius: 0.375rem;
              cursor: pointer;
              font-size: 0.875rem;
            "
          >
            Refresh Page
          </button>
        </div>
      </div>
    `
  }
}

// Start the application
initializeApp()

// Hot module replacement (development only)
if (import.meta.hot) {
  import.meta.hot.accept()
}
