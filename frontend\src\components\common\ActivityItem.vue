<template>
  <div class="flex items-start space-x-3">
    <div class="flex-shrink-0">
      <div class="h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
        <span class="text-xs font-medium text-primary-700 dark:text-primary-300">
          {{ activity.user?.charAt(0).toUpperCase() }}
        </span>
      </div>
    </div>
    <div class="min-w-0 flex-1">
      <p class="text-sm text-gray-900 dark:text-white">
        {{ activity.message }}
      </p>
      <p class="text-xs text-gray-500 dark:text-gray-400">
        {{ formatTime(activity.timestamp) }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Activity {
  id: number
  type: string
  message: string
  timestamp: string
  user?: string
}

interface Props {
  activity: Activity
}

defineProps<Props>()

const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return 'Just now'
  if (minutes < 60) return `${minutes}m ago`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}h ago`
  
  const days = Math.floor(hours / 24)
  return `${days}d ago`
}
</script>
