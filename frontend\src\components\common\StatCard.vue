<template>
  <div class="card">
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <div :class="iconClasses">
          <component :is="iconComponent" class="h-6 w-6" />
        </div>
      </div>
      <div class="ml-5 w-0 flex-1">
        <dl>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
            {{ title }}
          </dt>
          <dd class="flex items-baseline">
            <div class="text-2xl font-semibold text-gray-900 dark:text-white">
              {{ value }}
            </div>
            <div v-if="change" :class="changeClasses" class="ml-2 flex items-baseline text-sm font-semibold">
              <component :is="trendIcon" class="self-center flex-shrink-0 h-4 w-4" />
              <span class="sr-only">{{ trend === 'up' ? 'Increased' : 'Decreased' }} by</span>
              {{ change }}
            </div>
          </dd>
        </dl>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  UsersIcon, 
  CurrencyDollarIcon, 
  ShoppingBagIcon, 
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/vue/24/outline'

interface Props {
  title: string
  value: string
  change?: string
  trend?: 'up' | 'down'
  icon: string
  color: string
}

const props = defineProps<Props>()

const iconComponent = computed(() => {
  const icons = {
    users: UsersIcon,
    currency: CurrencyDollarIcon,
    shopping: ShoppingBagIcon,
    chart: ChartBarIcon,
  }
  return icons[props.icon as keyof typeof icons] || ChartBarIcon
})

const iconClasses = computed(() => {
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    purple: 'bg-purple-500',
    red: 'bg-red-500',
  }
  
  return [
    'flex items-center justify-center h-10 w-10 rounded-md text-white',
    colorClasses[props.color as keyof typeof colorClasses] || 'bg-gray-500'
  ]
})

const changeClasses = computed(() => {
  return props.trend === 'up' 
    ? 'text-green-600 dark:text-green-400'
    : 'text-red-600 dark:text-red-400'
})

const trendIcon = computed(() => {
  return props.trend === 'up' ? ArrowUpIcon : ArrowDownIcon
})
</script>
