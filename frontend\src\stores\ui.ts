/**
 * UI store for managing responsive behavior, layout state, and user interface preferences.
 * Handles sidebar state, mobile navigation, breakpoints, and UI interactions.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Breakpoint, UIPreferences } from '@types/ui'

export const useUiStore = defineStore('ui', () => {
  // State
  const currentBreakpoint = ref<Breakpoint>('lg')
  const windowWidth = ref(window.innerWidth)
  const windowHeight = ref(window.innerHeight)
  
  // Sidebar state
  const isSidebarCollapsed = ref(false)
  const isMobileSidebarOpen = ref(false)
  
  // Mobile navigation
  const showMobileBottomNav = ref(true)
  const activeBottomNavItem = ref('dashboard')
  
  // Modal and overlay state
  const activeModal = ref<string | null>(null)
  const isOverlayOpen = ref(false)
  
  // Loading and interaction states
  const isPageTransitioning = ref(false)
  const lastInteraction = ref(Date.now())
  
  // User preferences (persisted to localStorage)
  const preferences = ref<UIPreferences>({
    sidebarCollapsed: false,
    showMobileBottomNav: true,
    reducedMotion: false,
    highContrast: false,
    fontSize: 'medium',
    compactMode: false,
  })
  
  // Breakpoint definitions
  const breakpoints = {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536,
  }
  
  // Computed properties
  const isMobile = computed(() => {
    return currentBreakpoint.value === 'sm' || windowWidth.value < breakpoints.md
  })
  
  const isTablet = computed(() => {
    return currentBreakpoint.value === 'md' || 
           (windowWidth.value >= breakpoints.md && windowWidth.value < breakpoints.lg)
  })
  
  const isDesktop = computed(() => {
    return windowWidth.value >= breakpoints.lg
  })
  
  const isLargeScreen = computed(() => {
    return windowWidth.value >= breakpoints.xl
  })
  
  const sidebarWidth = computed(() => {
    if (isMobile.value) return '0px'
    return isSidebarCollapsed.value ? '64px' : '280px'
  })
  
  const contentPadding = computed(() => {
    if (isMobile.value) return '0px'
    return sidebarWidth.value
  })
  
  const isSmallHeight = computed(() => {
    return windowHeight.value < 600
  })
  
  const canShowSidebar = computed(() => {
    return !isMobile.value || isMobileSidebarOpen.value
  })
  
  const shouldShowMobileBottomNav = computed(() => {
    return isMobile.value && preferences.value.showMobileBottomNav && showMobileBottomNav.value
  })
  
  // Actions
  const updateBreakpoint = () => {
    const width = window.innerWidth
    windowWidth.value = width
    windowHeight.value = window.innerHeight
    
    if (width < breakpoints.sm) {
      currentBreakpoint.value = 'sm'
    } else if (width < breakpoints.md) {
      currentBreakpoint.value = 'md'
    } else if (width < breakpoints.lg) {
      currentBreakpoint.value = 'lg'
    } else if (width < breakpoints.xl) {
      currentBreakpoint.value = 'xl'
    } else {
      currentBreakpoint.value = '2xl'
    }
    
    // Auto-close mobile sidebar on desktop
    if (isDesktop.value && isMobileSidebarOpen.value) {
      isMobileSidebarOpen.value = false
    }
    
    // Auto-expand sidebar on large screens if it was collapsed due to space
    if (isLargeScreen.value && isSidebarCollapsed.value && !preferences.value.sidebarCollapsed) {
      isSidebarCollapsed.value = false
    }
  }
  
  const toggleSidebarCollapse = () => {
    isSidebarCollapsed.value = !isSidebarCollapsed.value
    preferences.value.sidebarCollapsed = isSidebarCollapsed.value
    savePreferences()
  }
  
  const toggleMobileSidebar = () => {
    isMobileSidebarOpen.value = !isMobileSidebarOpen.value
    updateLastInteraction()
  }
  
  const closeMobileSidebar = () => {
    isMobileSidebarOpen.value = false
    updateLastInteraction()
  }
  
  const openMobileSidebar = () => {
    isMobileSidebarOpen.value = true
    updateLastInteraction()
  }
  
  const setActiveBottomNavItem = (item: string) => {
    activeBottomNavItem.value = item
    updateLastInteraction()
  }
  
  const toggleMobileBottomNav = () => {
    showMobileBottomNav.value = !showMobileBottomNav.value
    preferences.value.showMobileBottomNav = showMobileBottomNav.value
    savePreferences()
  }
  
  const openModal = (modalId: string) => {
    activeModal.value = modalId
    isOverlayOpen.value = true
    document.body.style.overflow = 'hidden'
    updateLastInteraction()
  }
  
  const closeModal = () => {
    activeModal.value = null
    isOverlayOpen.value = false
    document.body.style.overflow = ''
    updateLastInteraction()
  }
  
  const setPageTransitioning = (transitioning: boolean) => {
    isPageTransitioning.value = transitioning
  }
  
  const updateLastInteraction = () => {
    lastInteraction.value = Date.now()
  }
  
  const updatePreferences = (newPreferences: Partial<UIPreferences>) => {
    preferences.value = { ...preferences.value, ...newPreferences }
    applyPreferences()
    savePreferences()
  }
  
  const applyPreferences = () => {
    // Apply sidebar state
    isSidebarCollapsed.value = preferences.value.sidebarCollapsed
    showMobileBottomNav.value = preferences.value.showMobileBottomNav
    
    // Apply accessibility preferences
    const root = document.documentElement
    
    if (preferences.value.reducedMotion) {
      root.style.setProperty('--transition-duration', '0s')
    } else {
      root.style.removeProperty('--transition-duration')
    }
    
    if (preferences.value.highContrast) {
      root.classList.add('high-contrast')
    } else {
      root.classList.remove('high-contrast')
    }
    
    // Apply font size
    root.setAttribute('data-font-size', preferences.value.fontSize)
    
    // Apply compact mode
    if (preferences.value.compactMode) {
      root.classList.add('compact-mode')
    } else {
      root.classList.remove('compact-mode')
    }
  }
  
  const savePreferences = () => {
    try {
      localStorage.setItem('ui-preferences', JSON.stringify(preferences.value))
    } catch (error) {
      console.warn('Failed to save UI preferences:', error)
    }
  }
  
  const loadPreferences = () => {
    try {
      const saved = localStorage.getItem('ui-preferences')
      if (saved) {
        const parsed = JSON.parse(saved)
        preferences.value = { ...preferences.value, ...parsed }
        applyPreferences()
      }
    } catch (error) {
      console.warn('Failed to load UI preferences:', error)
    }
  }
  
  const initializeFromStorage = () => {
    loadPreferences()
    updateBreakpoint()
  }
  
  const resetPreferences = () => {
    preferences.value = {
      sidebarCollapsed: false,
      showMobileBottomNav: true,
      reducedMotion: false,
      highContrast: false,
      fontSize: 'medium',
      compactMode: false,
    }
    applyPreferences()
    savePreferences()
  }
  
  // Utility methods
  const scrollToTop = (smooth = true) => {
    window.scrollTo({
      top: 0,
      behavior: smooth ? 'smooth' : 'auto'
    })
    updateLastInteraction()
  }
  
  const scrollToElement = (elementId: string, smooth = true) => {
    const element = document.getElementById(elementId)
    if (element) {
      element.scrollIntoView({
        behavior: smooth ? 'smooth' : 'auto',
        block: 'start'
      })
      updateLastInteraction()
    }
  }
  
  return {
    // State
    currentBreakpoint,
    windowWidth,
    windowHeight,
    isSidebarCollapsed,
    isMobileSidebarOpen,
    showMobileBottomNav,
    activeBottomNavItem,
    activeModal,
    isOverlayOpen,
    isPageTransitioning,
    lastInteraction,
    preferences,
    
    // Computed
    isMobile,
    isTablet,
    isDesktop,
    isLargeScreen,
    sidebarWidth,
    contentPadding,
    isSmallHeight,
    canShowSidebar,
    shouldShowMobileBottomNav,
    
    // Actions
    updateBreakpoint,
    toggleSidebarCollapse,
    toggleMobileSidebar,
    closeMobileSidebar,
    openMobileSidebar,
    setActiveBottomNavItem,
    toggleMobileBottomNav,
    openModal,
    closeModal,
    setPageTransitioning,
    updateLastInteraction,
    updatePreferences,
    initializeFromStorage,
    resetPreferences,
    scrollToTop,
    scrollToElement,
  }
})
