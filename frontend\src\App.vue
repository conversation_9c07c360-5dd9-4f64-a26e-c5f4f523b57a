<template>
  <div id="app" class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <!-- Loading overlay -->
    <LoadingOverlay v-if="appStore.isLoading" />
    
    <!-- Notification system -->
    <NotificationContainer />
    
    <!-- Main application layout -->
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div
        v-if="uiStore.isMobileSidebarOpen"
        class="fixed inset-0 z-40 lg:hidden"
        @click="uiStore.closeMobileSidebar"
      >
        <div class="absolute inset-0 bg-gray-600 opacity-75"></div>
      </div>
      
      <!-- Sidebar -->
      <AppSidebar />
      
      <!-- Main content area -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Header -->
        <AppHeader />
        
        <!-- Main content -->
        <main class="flex-1 overflow-y-auto focus:outline-none">
          <div class="py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <!-- Page content -->
              <RouterView v-slot="{ Component, route }">
                <Transition
                  name="page"
                  mode="out-in"
                  @enter="onPageEnter"
                  @leave="onPageLeave"
                >
                  <component
                    :is="Component"
                    :key="route.path"
                    class="w-full"
                  />
                </Transition>
              </RouterView>
            </div>
          </div>
        </main>
        
        <!-- Footer -->
        <AppFooter />
      </div>
    </div>
    
    <!-- Mobile bottom navigation (optional) -->
    <MobileBottomNav v-if="uiStore.showMobileBottomNav" class="lg:hidden" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, watch } from 'vue'
import { RouterView } from 'vue-router'
import { useAppStore } from '@stores/app'
import { useUiStore } from '@stores/ui'
import { useThemeStore } from '@stores/theme'
import { useAuthStore } from '@stores/auth'

// Components
import AppSidebar from '@components/layout/AppSidebar.vue'
import AppHeader from '@components/layout/AppHeader.vue'
import AppFooter from '@components/layout/AppFooter.vue'
import LoadingOverlay from '@components/common/LoadingOverlay.vue'
import NotificationContainer from '@components/common/NotificationContainer.vue'
import MobileBottomNav from '@components/layout/MobileBottomNav.vue'

// Stores
const appStore = useAppStore()
const uiStore = useUiStore()
const themeStore = useThemeStore()
const authStore = useAuthStore()

// Page transition handlers
const onPageEnter = (el: Element) => {
  // Add any page enter animations or logic
  console.log('Page entering:', el)
}

const onPageLeave = (el: Element) => {
  // Add any page leave animations or logic
  console.log('Page leaving:', el)
}

// Initialize application
onMounted(async () => {
  try {
    // Initialize app configuration
    await appStore.initialize()
    
    // Initialize theme
    themeStore.initializeTheme()
    
    // Check authentication status
    await authStore.checkAuthStatus()
    
    // Initialize UI state
    uiStore.initializeFromStorage()
    
  } catch (error) {
    console.error('Failed to initialize application:', error)
    appStore.setError('Failed to initialize application')
  }
})

// Watch for theme changes
watch(
  () => themeStore.isDarkMode,
  (isDark) => {
    document.documentElement.classList.toggle('dark', isDark)
  },
  { immediate: true }
)

// Watch for responsive breakpoint changes
watch(
  () => uiStore.currentBreakpoint,
  (breakpoint) => {
    // Handle responsive behavior changes
    if (breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl') {
      uiStore.closeMobileSidebar()
    }
  }
)

// Handle window resize for responsive behavior
const handleResize = () => {
  uiStore.updateBreakpoint()
}

// Add resize listener
onMounted(() => {
  window.addEventListener('resize', handleResize)
  handleResize() // Initial call
})

// Cleanup
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* Page transition animations */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* Responsive utilities */
@media (max-width: 640px) {
  .max-w-7xl {
    max-width: 100%;
  }
}

/* Dark mode transitions */
* {
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid theme('colors.blue.500');
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
</style>
