<template>
  <div class="dashboard-view">
    <!-- Page header -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            {{ pageTitle }}
          </h1>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            {{ pageDescription }}
          </p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <BaseButton
            variant="secondary"
            size="sm"
            @click="refreshData"
            :loading="isRefreshing"
          >
            <ArrowPathIcon class="h-4 w-4 mr-2" />
            Refresh
          </BaseButton>
          <BaseButton
            variant="primary"
            size="sm"
            @click="openCreateModal"
          >
            <PlusIcon class="h-4 w-4 mr-2" />
            New Item
          </BaseButton>
        </div>
      </div>
    </div>

    <!-- Stats grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
      <StatCard
        v-for="stat in stats"
        :key="stat.id"
        :title="stat.title"
        :value="stat.value"
        :change="stat.change"
        :trend="stat.trend"
        :icon="stat.icon"
        :color="stat.color"
      />
    </div>

    <!-- Main content grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- Chart section -->
      <div class="lg:col-span-2">
        <div class="card">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
              Analytics Overview
            </h2>
            <div class="flex space-x-2">
              <button
                v-for="period in chartPeriods"
                :key="period.value"
                @click="selectedPeriod = period.value"
                :class="[
                  'px-3 py-1 text-xs font-medium rounded-md transition-colors',
                  selectedPeriod === period.value
                    ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                    : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                ]"
              >
                {{ period.label }}
              </button>
            </div>
          </div>
          
          <!-- Chart placeholder -->
          <div class="h-64 sm:h-80 bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <ChartBarIcon class="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Chart will be rendered here
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Activity feed -->
      <div class="card">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">
          Recent Activity
        </h2>
        <div class="space-y-4">
          <ActivityItem
            v-for="activity in recentActivities"
            :key="activity.id"
            :activity="activity"
          />
        </div>
        <div class="mt-6">
          <router-link
            to="/activities"
            class="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300 font-medium"
          >
            View all activities →
          </router-link>
        </div>
      </div>
    </div>

    <!-- Data table -->
    <div class="card">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
          Recent Items
        </h2>
        <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
          <!-- Search -->
          <div class="relative">
            <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search items..."
              class="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          
          <!-- Filter -->
          <select
            v-model="selectedFilter"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">All Items</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="pending">Pending</option>
          </select>
        </div>
      </div>

      <!-- Responsive table -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Name
              </th>
              <th class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Status
              </th>
              <th class="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Created
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr
              v-for="item in filteredItems"
              :key="item.id"
              class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-8 w-8">
                    <div class="h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                      <span class="text-xs font-medium text-primary-700 dark:text-primary-300">
                        {{ item.name.charAt(0).toUpperCase() }}
                      </span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ item.name }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 sm:hidden">
                      {{ item.status }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="hidden sm:table-cell px-6 py-4 whitespace-nowrap">
                <span
                  :class="[
                    'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                    item.status === 'active'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : item.status === 'inactive'
                      ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  ]"
                >
                  {{ item.status }}
                </span>
              </td>
              <td class="hidden md:table-cell px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ formatDate(item.createdAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button
                    @click="editItem(item)"
                    class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                  >
                    Edit
                  </button>
                  <button
                    @click="deleteItem(item)"
                    class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                  >
                    Delete
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="mt-6 flex items-center justify-between">
        <div class="text-sm text-gray-700 dark:text-gray-300">
          Showing {{ (currentPage - 1) * itemsPerPage + 1 }} to {{ Math.min(currentPage * itemsPerPage, totalItems) }} of {{ totalItems }} results
        </div>
        <div class="flex space-x-2">
          <BaseButton
            variant="secondary"
            size="sm"
            :disabled="currentPage === 1"
            @click="previousPage"
          >
            Previous
          </BaseButton>
          <BaseButton
            variant="secondary"
            size="sm"
            :disabled="currentPage === totalPages"
            @click="nextPage"
          >
            Next
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  ArrowPathIcon,
  PlusIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
} from '@heroicons/vue/24/outline'

import { useAppStore } from '@stores/app'
import { useUiStore } from '@stores/ui'
import BaseButton from '@components/common/BaseButton.vue'
import StatCard from '@components/common/StatCard.vue'
import ActivityItem from '@components/common/ActivityItem.vue'

// Stores
const appStore = useAppStore()
const uiStore = useUiStore()

// Page configuration
const pageTitle = computed(() => `Welcome to ${appStore.config.name}`)
const pageDescription = computed(() => appStore.config.tagline)

// Local state
const isRefreshing = ref(false)
const searchQuery = ref('')
const selectedFilter = ref('')
const selectedPeriod = ref('7d')
const currentPage = ref(1)
const itemsPerPage = ref(10)

// Mock data (replace with real API calls)
const stats = ref([
  {
    id: 1,
    title: 'Total Users',
    value: '2,543',
    change: '+12%',
    trend: 'up',
    icon: 'users',
    color: 'blue'
  },
  {
    id: 2,
    title: 'Revenue',
    value: '$45,231',
    change: '+8%',
    trend: 'up',
    icon: 'currency',
    color: 'green'
  },
  {
    id: 3,
    title: 'Orders',
    value: '1,234',
    change: '-3%',
    trend: 'down',
    icon: 'shopping',
    color: 'yellow'
  },
  {
    id: 4,
    title: 'Conversion',
    value: '3.2%',
    change: '+0.5%',
    trend: 'up',
    icon: 'chart',
    color: 'purple'
  }
])

const chartPeriods = [
  { label: '7D', value: '7d' },
  { label: '30D', value: '30d' },
  { label: '90D', value: '90d' },
  { label: '1Y', value: '1y' }
]

const recentActivities = ref([
  {
    id: 1,
    type: 'user_created',
    message: 'New user registered',
    timestamp: new Date().toISOString(),
    user: 'John Doe'
  },
  {
    id: 2,
    type: 'order_completed',
    message: 'Order #1234 completed',
    timestamp: new Date(Date.now() - 3600000).toISOString(),
    user: 'Jane Smith'
  }
])

const items = ref([
  {
    id: 1,
    name: 'Sample Item 1',
    status: 'active',
    createdAt: new Date().toISOString()
  },
  {
    id: 2,
    name: 'Sample Item 2',
    status: 'inactive',
    createdAt: new Date(Date.now() - 86400000).toISOString()
  }
])

// Computed properties
const filteredItems = computed(() => {
  let filtered = items.value

  if (searchQuery.value) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (selectedFilter.value) {
    filtered = filtered.filter(item => item.status === selectedFilter.value)
  }

  return filtered
})

const totalItems = computed(() => filteredItems.value.length)
const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage.value))

// Methods
const refreshData = async () => {
  isRefreshing.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    // Refresh data here
  } finally {
    isRefreshing.value = false
  }
}

const openCreateModal = () => {
  uiStore.openModal('create-item')
}

const editItem = (item: any) => {
  console.log('Edit item:', item)
}

const deleteItem = (item: any) => {
  console.log('Delete item:', item)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

// Initialize
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.dashboard-view {
  @apply animate-fade-in;
}

/* Responsive table improvements */
@media (max-width: 640px) {
  .table-responsive {
    font-size: 0.875rem;
  }
}
</style>
