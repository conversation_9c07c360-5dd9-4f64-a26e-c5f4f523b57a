"""
Health check endpoints.
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import text
import time

from app.core.database import get_db
from app.core.config import settings

router = APIRouter()


@router.get("/")
async def health_check():
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "app_name": settings.app_name,
        "version": settings.app_version,
        "environment": settings.app_environment,
    }


@router.get("/detailed")
async def detailed_health_check(db: Session = Depends(get_db)):
    """Detailed health check including database connectivity."""
    health_data = {
        "status": "healthy",
        "timestamp": time.time(),
        "app_name": settings.app_name,
        "version": settings.app_version,
        "environment": settings.app_environment,
        "checks": {
            "database": "unknown",
        }
    }
    
    # Check database connectivity
    try:
        db.execute(text("SELECT 1"))
        health_data["checks"]["database"] = "healthy"
    except Exception as e:
        health_data["status"] = "unhealthy"
        health_data["checks"]["database"] = f"unhealthy: {str(e)}"
    
    return health_data
