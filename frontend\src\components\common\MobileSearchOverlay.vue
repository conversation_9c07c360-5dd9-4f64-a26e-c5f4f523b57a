<template>
  <div class="fixed inset-0 z-50 bg-white dark:bg-gray-900">
    <div class="flex items-center p-4 border-b border-gray-200 dark:border-gray-700">
      <input
        :value="modelValue"
        @input="$emit('update:modelValue', ($event.target as HTMLInputElement).value)"
        type="search"
        placeholder="Search..."
        class="flex-1 border-none bg-transparent text-lg focus:outline-none"
        autofocus
      />
      <button @click="$emit('close')" class="ml-4 text-gray-500">
        Cancel
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  modelValue: string
}>()

defineEmits<{
  'update:modelValue': [value: string]
  close: []
  search: []
}>()
</script>
