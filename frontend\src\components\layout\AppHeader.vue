<template>
  <header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 h-16 flex items-center justify-between px-4 lg:px-6">
    <!-- Left section -->
    <div class="flex items-center space-x-4">
      <!-- Mobile menu button -->
      <button
        v-if="uiStore.isMobile"
        @click="uiStore.toggleMobileSidebar"
        class="p-2 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors lg:hidden"
        aria-label="Open sidebar"
      >
        <Bars3Icon class="h-6 w-6" />
      </button>
      
      <!-- Breadcrumb navigation -->
      <nav class="hidden sm:flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2">
          <li v-for="(item, index) in breadcrumbs" :key="item.name" class="flex items-center">
            <router-link
              v-if="item.href && index < breadcrumbs.length - 1"
              :to="item.href"
              class="text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            >
              {{ item.name }}
            </router-link>
            <span
              v-else
              class="text-sm font-medium text-gray-900 dark:text-white"
            >
              {{ item.name }}
            </span>
            <ChevronRightIcon
              v-if="index < breadcrumbs.length - 1"
              class="h-4 w-4 text-gray-400 mx-2"
            />
          </li>
        </ol>
      </nav>
      
      <!-- Page title (mobile) -->
      <h1 class="text-lg font-semibold text-gray-900 dark:text-white sm:hidden">
        {{ currentPageTitle }}
      </h1>
    </div>
    
    <!-- Center section - Search (desktop) -->
    <div class="hidden md:flex flex-1 max-w-lg mx-8">
      <div class="relative w-full">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
        </div>
        <input
          v-model="searchQuery"
          type="search"
          placeholder="Search..."
          class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
          @keyup.enter="performSearch"
        />
      </div>
    </div>
    
    <!-- Right section -->
    <div class="flex items-center space-x-3">
      <!-- Search button (mobile) -->
      <button
        v-if="uiStore.isMobile"
        @click="toggleMobileSearch"
        class="p-2 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors md:hidden"
        aria-label="Search"
      >
        <MagnifyingGlassIcon class="h-5 w-5" />
      </button>
      
      <!-- Notifications -->
      <div class="relative">
        <button
          @click="toggleNotifications"
          class="p-2 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors relative"
          aria-label="Notifications"
        >
          <BellIcon class="h-5 w-5" />
          <!-- Notification badge -->
          <span
            v-if="authStore.unreadNotifications > 0"
            class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"
          >
            {{ authStore.unreadNotifications > 9 ? '9+' : authStore.unreadNotifications }}
          </span>
        </button>
        
        <!-- Notifications dropdown -->
        <NotificationsDropdown
          v-if="showNotifications"
          @close="showNotifications = false"
        />
      </div>
      
      <!-- User menu -->
      <div class="relative">
        <button
          @click="toggleUserMenu"
          class="flex items-center space-x-2 p-2 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          aria-label="User menu"
        >
          <img
            :src="authStore.user?.avatar || '/default-avatar.png'"
            :alt="authStore.user?.name || 'User'"
            class="h-6 w-6 rounded-full"
          />
          <span class="hidden sm:block text-sm font-medium text-gray-900 dark:text-white">
            {{ authStore.user?.name || 'User' }}
          </span>
          <ChevronDownIcon class="h-4 w-4" />
        </button>
        
        <!-- User dropdown -->
        <UserDropdown
          v-if="showUserMenu"
          @close="showUserMenu = false"
        />
      </div>
    </div>
    
    <!-- Mobile search overlay -->
    <MobileSearchOverlay
      v-if="showMobileSearch"
      v-model="searchQuery"
      @close="showMobileSearch = false"
      @search="performSearch"
    />
  </header>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Bars3Icon,
  MagnifyingGlassIcon,
  BellIcon,
  ChevronRightIcon,
  ChevronDownIcon,
} from '@heroicons/vue/24/outline'

import { useUiStore } from '@stores/ui'
import { useAuthStore } from '@stores/auth'
import { useSearchStore } from '@stores/search'
import NotificationsDropdown from '@components/common/NotificationsDropdown.vue'
import UserDropdown from '@components/common/UserDropdown.vue'
import MobileSearchOverlay from '@components/common/MobileSearchOverlay.vue'

// Stores
const uiStore = useUiStore()
const authStore = useAuthStore()
const searchStore = useSearchStore()

// Router
const route = useRoute()
const router = useRouter()

// Local state
const searchQuery = ref('')
const showNotifications = ref(false)
const showUserMenu = ref(false)
const showMobileSearch = ref(false)

// Computed properties
const currentPageTitle = computed(() => {
  return route.meta?.title as string || 'Dashboard'
})

const breadcrumbs = computed(() => {
  const crumbs = []
  const pathSegments = route.path.split('/').filter(Boolean)
  
  // Add home
  crumbs.push({ name: 'Home', href: '/' })
  
  // Add path segments
  let currentPath = ''
  for (const segment of pathSegments) {
    currentPath += `/${segment}`
    const routeRecord = router.resolve(currentPath)
    crumbs.push({
      name: routeRecord.meta?.title as string || segment.charAt(0).toUpperCase() + segment.slice(1),
      href: currentPath,
    })
  }
  
  return crumbs
})

// Methods
const performSearch = () => {
  if (searchQuery.value.trim()) {
    searchStore.performSearch(searchQuery.value.trim())
    showMobileSearch.value = false
  }
}

const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value
  showUserMenu.value = false
}

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
  showNotifications.value = false
}

const toggleMobileSearch = () => {
  showMobileSearch.value = !showMobileSearch.value
}

// Close dropdowns when clicking outside
const closeDropdowns = () => {
  showNotifications.value = false
  showUserMenu.value = false
}

// Add click outside listener
onMounted(() => {
  document.addEventListener('click', closeDropdowns)
})

onUnmounted(() => {
  document.removeEventListener('click', closeDropdowns)
})
</script>

<style scoped>
/* Ensure header stays on top */
header {
  z-index: 20;
}

/* Search input focus styles */
input[type="search"]:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Notification badge animation */
.notification-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
</style>
