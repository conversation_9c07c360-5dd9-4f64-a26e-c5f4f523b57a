# =============================================================================
# REALITY 2.0 - ENVIRONMENT CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file to .env and configure your environment-specific values
# Never commit .env files to version control!

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
APP_NAME="Reality 2.0"
APP_VERSION="1.0.0"
APP_DESCRIPTION="High-End Web Application"
APP_ENVIRONMENT="development"  # development, staging, production

# =============================================================================
# BACKEND CONFIGURATION
# =============================================================================

# Server Settings
BACKEND_HOST="0.0.0.0"
BACKEND_PORT=8000
BACKEND_RELOAD=true
BACKEND_LOG_LEVEL="info"  # debug, info, warning, error, critical

# Database Configuration
DATABASE_URL="sqlite:///./reality.db"  # For development
# DATABASE_URL="postgresql://user:password@localhost:5432/reality"  # For production
DATABASE_ECHO=false
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Security Settings
SECRET_KEY="your-super-secret-key-change-this-in-production"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Settings
CORS_ORIGINS="http://localhost:3000,http://127.0.0.1:3000"
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS="GET,POST,PUT,DELETE,OPTIONS"
CORS_ALLOW_HEADERS="*"

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60  # seconds

# Email Configuration (Optional)
SMTP_HOST=""
SMTP_PORT=587
SMTP_USERNAME=""
SMTP_PASSWORD=""
SMTP_FROM_EMAIL=""
SMTP_FROM_NAME=""

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES="jpg,jpeg,png,gif,pdf,doc,docx"
UPLOAD_DIRECTORY="./uploads"

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================

# API Configuration
VITE_API_BASE_URL="http://localhost:8000"
VITE_API_VERSION="v1"
VITE_API_TIMEOUT=30000  # milliseconds

# Application Branding
VITE_APP_NAME="Reality 2.0"
VITE_APP_TAGLINE="The Future of Web Applications"
VITE_APP_LOGO_URL="/logo.svg"
VITE_APP_FAVICON_URL="/favicon.ico"

# Theme Configuration
VITE_THEME_PRIMARY_COLOR="#3B82F6"  # Blue
VITE_THEME_SECONDARY_COLOR="#10B981"  # Green
VITE_THEME_ACCENT_COLOR="#F59E0B"  # Amber
VITE_THEME_ERROR_COLOR="#EF4444"  # Red
VITE_THEME_WARNING_COLOR="#F97316"  # Orange
VITE_THEME_SUCCESS_COLOR="#22C55E"  # Green
VITE_THEME_INFO_COLOR="#06B6D4"  # Cyan

# Layout Settings
VITE_LAYOUT_SIDEBAR_WIDTH="280px"
VITE_LAYOUT_HEADER_HEIGHT="64px"
VITE_LAYOUT_FOOTER_HEIGHT="48px"
VITE_LAYOUT_CONTAINER_MAX_WIDTH="1200px"

# Feature Flags
VITE_FEATURE_DARK_MODE=true
VITE_FEATURE_NOTIFICATIONS=true
VITE_FEATURE_REAL_TIME=true
VITE_FEATURE_PWA=true
VITE_FEATURE_ANALYTICS=false

# Performance Settings
VITE_CACHE_DURATION=300000  # 5 minutes in milliseconds
VITE_DEBOUNCE_DELAY=300  # milliseconds
VITE_PAGINATION_SIZE=20
VITE_INFINITE_SCROLL_THRESHOLD=100  # pixels

# Social Media & External Links
VITE_SOCIAL_GITHUB=""
VITE_SOCIAL_TWITTER=""
VITE_SOCIAL_LINKEDIN=""
VITE_CONTACT_EMAIL=""
VITE_SUPPORT_URL=""
VITE_DOCUMENTATION_URL=""

# Analytics (Optional)
VITE_ANALYTICS_GOOGLE_ID=""
VITE_ANALYTICS_MIXPANEL_TOKEN=""
VITE_ANALYTICS_HOTJAR_ID=""

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# Database Admin (pgAdmin)
PGADMIN_DEFAULT_EMAIL="<EMAIL>"
PGADMIN_DEFAULT_PASSWORD="admin123"
PGADMIN_PORT=8080

# Redis (for caching/sessions)
REDIS_URL="redis://localhost:6379/0"
REDIS_PASSWORD=""
REDIS_DB=0

# Monitoring & Logging
LOG_LEVEL="info"
LOG_FORMAT="json"  # json, text
LOG_FILE_PATH="./logs/app.log"
LOG_MAX_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================
# These should be overridden in production environments

# Security Headers
SECURITY_HSTS_MAX_AGE=31536000
SECURITY_CONTENT_TYPE_NOSNIFF=true
SECURITY_FRAME_OPTIONS="DENY"
SECURITY_XSS_PROTECTION=true

# SSL/TLS
SSL_REDIRECT=false  # Set to true in production
SSL_CERT_PATH=""
SSL_KEY_PATH=""

# CDN & Static Files
CDN_URL=""
STATIC_FILES_URL=""
MEDIA_FILES_URL=""

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=""
BACKUP_S3_REGION=""

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Docker Compose Settings
COMPOSE_PROJECT_NAME="reality-2.0"
COMPOSE_FILE="docker-compose.yml"

# Container Settings
POSTGRES_DB="reality"
POSTGRES_USER="reality_user"
POSTGRES_PASSWORD="reality_password"
POSTGRES_PORT=5432

# =============================================================================
# NOTES
# =============================================================================
# 1. Change all default passwords and secrets in production
# 2. Use strong, randomly generated values for SECRET_KEY
# 3. Configure proper CORS origins for production
# 4. Set up proper SSL certificates for production
# 5. Configure monitoring and logging for production
# 6. Regular backup strategy is essential for production
# 7. Use environment-specific .env files (.env.development, .env.production)
# =============================================================================
