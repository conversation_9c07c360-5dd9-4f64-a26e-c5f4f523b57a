"""
Database configuration and session management.
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from app.core.config import settings

# Create database engines
if settings.database_url.startswith("sqlite"):
    # SQLite configuration
    engine = create_engine(
        settings.database_url,
        connect_args={"check_same_thread": False},
        echo=settings.database_echo,
    )
    async_engine = None
else:
    # PostgreSQL configuration
    engine = create_engine(
        settings.database_url_sync,
        pool_size=settings.database_pool_size,
        max_overflow=settings.database_max_overflow,
        echo=settings.database_echo,
    )
    
    # Async engine for PostgreSQL
    async_engine = create_async_engine(
        settings.database_url,
        pool_size=settings.database_pool_size,
        max_overflow=settings.database_max_overflow,
        echo=settings.database_echo,
    )

# Create session makers
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

if async_engine:
    AsyncSessionLocal = async_sessionmaker(
        async_engine, class_=AsyncSession, expire_on_commit=False
    )
else:
    AsyncSessionLocal = None

# Create declarative base
Base = declarative_base()


# Dependency to get database session
def get_db():
    """Get database session for synchronous operations."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db():
    """Get database session for asynchronous operations."""
    if AsyncSessionLocal is None:
        raise RuntimeError("Async database session not configured")
    
    async with AsyncSessionLocal() as session:
        yield session


async def create_tables():
    """Create database tables."""
    if async_engine:
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
    else:
        Base.metadata.create_all(bind=engine)


async def drop_tables():
    """Drop database tables."""
    if async_engine:
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
    else:
        Base.metadata.drop_all(bind=engine)
