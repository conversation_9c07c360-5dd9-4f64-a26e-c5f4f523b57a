# File Structure Rules for High-End Applications

## Consistency & Naming Rules

### Rule 1: Rigid Convention Adherence
**RULE:** Once you choose a naming convention (singular vs plural directories, PascalCase for components, snake_case for Python modules), stick to it rigidly throughout the entire project - inconsistency is a major source of confusion and development slowdown.

### Rule 2: Balanced Hierarchy
**RULE:** Avoid excessively deep nesting (more than 3-4 levels) which makes paths long and navigation cumbersome, and avoid overly flat structures that cram too many unrelated files into one directory.

### Rule 3: Feature-Based Grouping
**RULE:** For larger applications, prioritize grouping files by feature/domain over grouping purely by type - when working on a feature, all related files should be co-located for easier development and reasoning.

### Rule 4: Descriptive Naming
**RULE:** Directory and file names should clearly indicate their content or purpose - avoid generic names like 'utils' if content can be more specific (e.g., email_utils, file_upload_helpers).

### Rule 5: Dedicated Configuration
**RULE:** Dedicate a specific location (e.g., config/ directory or core/config.py) for all application configuration to make it easy to understand and modify application behavior without digging through code.

### Rule 6: Consistent Test Placement
**RULE:** Either have a top-level tests/ directory that mirrors your source code structure, or place tests directly next to the code they test - choose one approach and stick to it consistently.

### Rule 7: Third-Party Isolation
**RULE:** Never directly modify third-party library files - if you need to patch or extend something, wrap it in your own code or use official extension points to prevent breaking updates.

### Rule 8: Generated Files Separation
**RULE:** All generated files (build outputs, compiled code, cache files, logs) should be directed to specific, easily identifiable directories (dist/, build/, target/, logs/) and be explicitly ignored by version control.

### Rule 9: Essential Root Files
**RULE:** Essential project-level files should be in the root directory (README.md, LICENSE, .gitignore, package.json, pyproject.toml, Dockerfile, docker-compose.yml) to provide immediate context and instructions.

### Rule 10: Clean Root Directory
**RULE:** Keep the root directory as clean as possible, only placing essential project configuration and documentation files there - move application code into dedicated source directories (src/, app/).

### Rule 11: Clear Content Indication
**RULE:** Folder and file names should clearly indicate their content or purpose while being as short as possible - avoid ambiguity and names like _final_final, new_copy, temp.

### Rule 12: Build Artifacts Separation
**RULE:** Keep automatically generated files (build outputs, cache files, logs, dependency installations) separate from source code in directories like dist/, build/, node_modules/, __pycache__/, logs/.

### Rule 13: Documentation Requirements
**RULE:** Every significant directory, and certainly the project root, should have a README.md explaining its purpose, how to use its contents, and any important conventions for onboarding and maintenance.

### Rule 14: Relative Path Usage
**RULE:** Where possible, use relative file paths within your codebase (./components/Button.vue) instead of absolute paths to make your project more portable and easier to refactor.

### Rule 15: Safe Character Usage
**RULE:** Use hyphens (-), underscores (_), or camelCase for names - avoid spaces or special characters (!, @, #, $, %, &, *, etc.) which can cause issues across different operating systems and build tools.

### Rule 16: Date Format Consistency
**RULE:** For files that are versioned or dated (logs, backups, reports), use a consistent, sortable date format like YYYY-MM-DD or YYYYMMDD to ensure chronological sorting and easy identification.

## Technology-Specific Structure Rules

### FastAPI Backend Structure
- **Entry Point**: `app/main.py` - FastAPI app initialization and router inclusion
- **Core**: `core/` - config.py, database.py, security.py, constants.py
- **API Layer**: `api/v1/endpoints/` - versioned route definitions with deps.py for dependencies
- **Data Layer**: 
  - `schemas/` - Pydantic models for API requests/responses
  - `models/` - SQLAlchemy database models
  - `crud/` - Database operation functions
- **Business Logic**: `services/` - orchestrates CRUD operations with business rules
- **Support**: `tests/`, `utils/`, `alembic/` for migrations

### Vue.js Frontend Structure
- **Components**: Organized by scope (common/, layout/, specific/)
- **Views**: Page-level components mapped to routes
- **State**: `store/` for Pinia/Vuex modules
- **Navigation**: `router/` for Vue Router configuration
- **API**: `services/` for centralized API communication
- **Assets**: Processed static files and global styles
- **Utils**: Frontend-specific helper functions

## Monorepo Structure Template
```
/your-high-end-app/
├── backend/                  # FastAPI backend
├── frontend/                 # Vue.js frontend
├── .env.example              # Example environment variables
├── .dockerignore             # Files to ignore when building Docker image
├── docker-compose.yml        # For local development with Docker
├── Dockerfile.backend        # Dockerfile for FastAPI app
├── Dockerfile.frontend       # Dockerfile for Vue.js app
├── README.md                 # Project overview, setup instructions
└── ... other project-level config
```

## Benefits of Following These Rules
- **Scalability**: Easy to add features without disrupting existing code
- **Maintainability**: Code is easy to find, understand, and modify
- **Team Collaboration**: Backend and frontend teams can work independently
- **Testability**: Modular structure supports comprehensive testing
- **Onboarding**: New developers can quickly understand project layout
- **Consistency**: Predictable structure reduces cognitive overhead
- **Portability**: Projects can be moved and refactored easily
